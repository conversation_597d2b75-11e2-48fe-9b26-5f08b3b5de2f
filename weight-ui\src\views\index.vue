<template>
<div class="external-page-container">
    <!-- 网络连接正常时显示外部页面 -->
    <externalPage 
        v-if="isOnline" 
        :externalUrl="externalUrl" 
        :iframeTitle="iframeTitle">
    </externalPage>
    
    <!-- 网络连接异常时显示图片 -->
    <div v-else class="offline-container">
        <img :src="offlineImage" alt="网络连接异常" class="offline-image">
        <div class="offline-text">
            <h3>网络连接异常</h3>
            <p>请检查网络连接后刷新页面</p>
            <el-button @click="checkNetwork" type="primary" size="small">重新检测</el-button>
        </div>
    </div>
</div>
</template>

  
<script>
import externalPage  from '../components/ExternalPageEmbed/index.vue'
export default {
    name: 'indexHome',
    components: {
        externalPage
    },
    data() {
        return {
            // externalUrl: 'http://www.hxczkj.cn/', // 外部页面的URLhttp://*************:81/
            externalUrl: 'http://www.hxczkj.cn/', // 外部页面的URL
            iframeTitle: '外部页面', // iframe的title属性
            isOnline: true, // 网络连接状态
            offlineImage: require('@/assets/images/info.jpg'), // 离线时显示的图片
            checkTimer: null // 定时器
        }
    },
    mounted() {
        // 在Electron环境中添加调试信息
        if (window.electronAPI || window.require) {
            console.log('Electron环境 - 首页组件已挂载')
        }

        this.checkNetwork()
        // 定期检测网络状态
        this.checkTimer = setInterval(() => {
            this.checkNetwork()
        }, 30000) // 每30秒检测一次
    },
    beforeDestroy() {
        if (this.checkTimer) {
            clearInterval(this.checkTimer)
        }
    },
    methods: {
        // 检测网络连接状态
        async checkNetwork() {
            try {
                // 尝试请求一个简单的接口或者ping一个可靠的服务
                await fetch(this.externalUrl, {
                    method: 'HEAD',
                    mode: 'no-cors',
                    cache: 'no-cache',
                    timeout: 5000
                })
                this.isOnline = true
                console.log('网络连接正常')
            } catch (error) {
                // 如果请求失败，再尝试请求其他可靠的服务
                try {
                    await fetch('https://www.baidu.com', {
                        method: 'HEAD',
                        mode: 'no-cors',
                        cache: 'no-cache',
                        timeout: 5000
                    })
                    this.isOnline = true
                    console.log('网络连接正常（备用检测）')
                } catch (err) {
                    this.isOnline = false
                    console.log('网络连接异常')
                }
            }
        }
    }
}
</script>

<style scoped>
.external-page-container {
    width: 100%;
    height: 100%;
}

.offline-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: calc(100vh - 100px);
    text-align: center;
    padding: 20px;
}

.offline-image {
    max-width: 400px;
    max-height: 300px;
    width: auto;
    height: auto;
    margin-bottom: 20px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.offline-text {
    color: #666;
}

.offline-text h3 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 24px;
}

.offline-text p {
    margin: 0 0 20px 0;
    font-size: 16px;
    color: #999;
}
</style>
