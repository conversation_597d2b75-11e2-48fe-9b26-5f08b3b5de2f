<template>
<div class="navbar">
    <!-- <hamburger id="hamburger-container" :is-active="sidebar.opened" class="hamburger-container" @toggleClick="toggleSideBar" />

    <breadcrumb v-if="!topNav" id="breadcrumb-container" class="breadcrumb-container" />
    <top-nav v-if="topNav" id="topmenu-container" class="topmenu-container" /> -->
    <div v-wResize="onMenuContResize" id="navbar-left" class="navbar__left">
        <top-nav v-if="menuContWidth" id="topmenu-container" class="topmenu-container" :width="menuContWidth" />
    </div>

    <div class="right-menu">
        <template v-if="device!=='mobile'">
            <search id="header-search" class="right-menu-item" />

            <screenfull id="screenfull" class="right-menu-item hover-effect" />
            <el-tooltip content="语言切换" effect="dark" placement="bottom">
                <lang-switch id="size-select" class="right-menu-item hover-effect" />
            </el-tooltip>
            <el-tooltip content="布局大小" effect="dark" placement="bottom">
                <size-select id="size-select" class="right-menu-item hover-effect" />
            </el-tooltip>

        </template>

        <el-dropdown class="avatar-container right-menu-item hover-effect" trigger="click" @visible-change="onVisibleChange">
            <div class="avatar-wrapper">
                <img :src="avatar" class="user-avatar">
                <i class="el-icon-caret-bottom" />
            </div>
            <el-dropdown-menu slot="dropdown">
                <router-link to="/user/profile">
                    <el-dropdown-item>个人中心</el-dropdown-item>
                </router-link>
                <el-dropdown-item @click.native="setting = true">
                    <span>布局设置</span>
                </el-dropdown-item>
                <el-dropdown-item divided @click.native="logout">
                    <span>退出登录</span>
                </el-dropdown-item>
            </el-dropdown-menu>
        </el-dropdown>
    </div>
</div>
</template>

<script>
import {
    mapGetters
} from 'vuex'
import Breadcrumb from '@/components/Breadcrumb'
import TopNav from '@/components/TopNav'
import Hamburger from '@/components/Hamburger'
import Screenfull from '@/components/Screenfull'
import SizeSelect from '@/components/SizeSelect'
import langSwitch from '@/components/langSwitch/index.vue'
import Search from '@/components/HeaderSearch'
import RuoYiGit from '@/components/RuoYi/Git'
import RuoYiDoc from '@/components/RuoYi/Doc'

export default {
    components: {
        Breadcrumb,
        TopNav,
        Hamburger,
        Screenfull,
        SizeSelect,
        Search,
        RuoYiGit,
        RuoYiDoc,
        langSwitch
    },
    data() {
        return {
            menuContWidth: 0,
        };
    },

    computed: {
        ...mapGetters([
            'sidebar',
            'avatar',
            'device'
        ]),
        setting: {
            get() {
                return this.$store.state.settings.showSettings
            },
            set(val) {
                this.$store.dispatch('settings/changeSetting', {
                    key: 'showSettings',
                    value: val
                })
            }
        },
        topNav: {
            get() {
                return this.$store.state.settings.topNav
            }
        }
    },
    methods: {
        // 菜单容器宽度变化回调
        onMenuContResize({
            width
        }) {
            this.menuContWidth = width;
        },
        toggleSideBar() {
            this.$store.dispatch('app/toggleSideBar')
        },
        onVisibleChange(val) {
            if(val){
                this.$hidPlugin()
            }else{
                this.$showPlugin()
            }
            console.log('visible', val)
        },
        logout() {
            this.$confirm('确定注销并退出系统吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$store.dispatch('LogOut').then(() => {
                    // 在Electron环境中使用Vue Router进行跳转，避免使用location.href
                    if (window.electronAPI || window.require) {
                        // Electron环境
                        this.$router.push('/login').catch(() => {
                            // 如果路由跳转失败，尝试跳转到根路径
                            this.$router.push('/').catch(() => {
                                // 最后的备选方案：重新加载页面
                                window.location.reload()
                            })
                        })
                    } else {
                        // Web环境
                        this.$router.push('/login')
                    }
                })
            }).catch(() => {})
        }
    }
}
</script>

<style lang="scss" scoped>
.navbar {
    height: 50px;
    overflow: hidden;
    position: relative;
    background: #1890ff;
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
    display: flex;

    &__left {
        flex: 1;
        overflow: hidden;
        margin-left: 10px;
        margin-right: 20px;
    }

    .hamburger-container {
        line-height: 46px;
        height: 100%;
        float: left;
        cursor: pointer;
        transition: background 0.3s;
        -webkit-tap-highlight-color: transparent;

        &:hover {
            background: rgba(0, 0, 0, 0.025);
        }
    }

    .breadcrumb-container {
        float: left;
    }

    .topmenu-container {
        display: inline-flex;
        align-items: center;
        height: 50px;
        border: none;
    }

    .errLog-container {
        display: inline-block;
        vertical-align: top;
    }

    .right-menu {
        flex-shrink: 0;
        margin-left: auto;
        height: 100%;
        display: flex;
        align-items: center;

        &:focus {
            outline: none;
        }

        .right-menu-item {
            display: inline-flex;
            align-items: center;
            padding: 0 8px;
            height: 100%;
            font-size: 18px;
            color: #fff;

            &.hover-effect {
                cursor: pointer;
                transition: background 0.3s;

                &:hover {
                    background: rgba(0, 0, 0, 0.025);
                }
            }
        }

        .avatar-container {
            margin-right: 30px;

            .avatar-wrapper {
                margin-top: 5px;
                position: relative;

                .user-avatar {
                    cursor: pointer;
                    width: 40px;
                    height: 40px;
                    border-radius: 10px;
                }

                .el-icon-caret-bottom {
                    cursor: pointer;
                    position: absolute;
                    right: -20px;
                    top: 25px;
                    font-size: 12px;
                }
            }
        }
    }
}
</style>
