<template>
<div class="doubleBalance">
    <el-container>
        <el-main class="el-main" :style="theme">
            <div class="top_body">
                <!-- 左侧称重区域 -->
                <div class="weight_container left-weight">
                    <div class="form_container">
                        <div class="weight_info">
                            <div class="weight_num">{{ leftWeightNum }}</div>
                            <div class="steady_state">
                                <div class="steady_state_text">单位(kg)</div>
                                <div :class="['steady_state_round', !leftSteadyState ? 'red_color' : '']"></div>
                                <div class="steady_state_text">连接</div>
                                <div :class="['steady_state_round', leftSteadyState ? 'green_color' : '']"></div>
                                <div class="steady_state_text">稳定</div>
                            </div>
                        </div>
                        <div class="weight_form">
                            <el-form :model="leftForm" label-width="100px" :rules="rules" ref="leftForm">
                                <el-row :gutter="20">
                                    <el-col :span="15">
                                        <el-form-item label="车牌号码" prop="licensePlate">
                                            <el-select @focus="getLicensePlate" v-model="leftForm.licensePlate" filterable clearable allow-create autocomplete default-first-option placeholder="请输入车牌号" @change="licensePlateCallbackFun('left', leftForm.licensePlate)">
                                                <el-option v-for="item in licensePlateOptions" :key="item.licensePlate" :label="item.licensePlate" :value="item.licensePlate">
                                                </el-option>
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="9">
                                        <el-form-item label="毛重" class="form_item">
                                            <el-input v-model="leftForm.weightM" clearable placeholder="请输入毛重" :disabled="true"></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row :gutter="20">
                                    <el-col :span="15">
                                        <el-form-item label="供应商名" prop="supplierId">
                                            <el-select @focus="getSupplier" v-model="leftForm.supplierId" filterable clearable allow-create default-first-option placeholder="请输入供应商名">
                                                <el-option v-for="item in supplierOptions" :key="item.supplierId" :label="item.supplierName" :value="item.supplierId">
                                                </el-option>
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="9">
                                        <el-form-item label="皮重" class="form_item">
                                            <el-input v-model="leftForm.weightP" placeholder="请输入皮重" clearable :disabled="true"></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row :gutter="20">
                                    <el-col :span="15">
                                        <el-form-item label="客户名称" prop="customerId">
                                            <el-select @focus="getCustomer" v-model="leftForm.customerId" filterable clearable allow-create default-first-option placeholder="请输入客户名称">
                                                <el-option v-for="item in customerOptions" :key="item.customerId" :label="item.customerName" :value="item.customerId">
                                                </el-option>
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="9">
                                        <el-form-item label="净重" class="form_item">
                                            <el-input v-model="leftForm.netWeight" clearable placeholder="请输入净重" :disabled="true"></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row :gutter="20">
                                    <el-col :span="15">
                                        <el-form-item label="货物名称" prop="materialId">
                                            <el-select @focus="getGoodsName" v-model="leftForm.materialId" @change="materialChange('left', leftForm.materialId)" filterable clearable allow-create default-first-option placeholder="请输入货物名称">
                                                <el-option v-for="item in goodsNameOptions" :key="item.materialId" :label="item.materialName" :value="item.materialId">
                                                </el-option>
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="9">
                                        <el-form-item label="扣率" prop="cutPer" class="form_item" v-if="deductionMode == 'cutPer'">
                                            <el-input type="number" min="0" max="100" v-model="leftForm.cutPer" clearable placeholder="请输入扣率"></el-input>
                                        </el-form-item>
                                        <el-form-item label="扣重" class="form_item" v-else>
                                            <el-input type="number" min="0" v-model="leftForm.cutWeight" clearable placeholder="请输入扣重"></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row :gutter="20">
                                    <el-col :span="15">
                                        <el-form-item label="规格名称">
                                            <el-select @focus="getSpecificationName(leftForm.materialId)" v-model="leftForm.specId" filterable clearable allow-create default-first-option placeholder="请输入规格名称">
                                                <el-option v-for="item in specificationNameOptions" :key="item.specId" :label="item.specName" :value="item.specId">
                                                </el-option>
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="9">
                                        <el-form-item label="备注" class="form_item">
                                            <el-input v-model="leftForm.remark" clearable placeholder="请输入备注"></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                            </el-form>
                        </div>
                    </div>
                    <div class="operation_container">
                        <div class="equipment_status">
                            <div>地感 <span :class="[leftFrontCoil != 'OFF'?'green_color': 'red_color']"></span></div>
                            <div>光栅 <span :class="[leftFrontRaster != 'OFF'?'green_color': 'red_color']"></span></div>
                            <div>地感 <span :class="[leftBackCoil != 'OFF'?'green_color': 'red_color']"></span></div>
                            <div>光栅 <span :class="[leftBackRaster != 'OFF'?'green_color': 'red_color']"></span></div>
                        </div>
                        <div class="operation_type">
                            <div class="overweight_type">
                                <div class="type_title">过磅类型:</div>
                                <el-radio-group v-model="leftForm.weightType" @change="weightTypeChange('left', leftForm.weightType, true)">
                                    <el-radio :label="item.value" size="medium" v-for="item in dict.type.business_type" :key="item.value">{{item.label}}</el-radio>
                                </el-radio-group>
                            </div>
                            <div class="overweight_type">
                                <div class="type_title">过磅模式:</div>
                                <el-radio-group :disabled="leftModelStatus" v-model="leftForm.weightMode" @change="weighingModeChange('left')">
                                    <el-radio :label="item.value" size="medium" v-for="item in dict.type.weighing_mode" :key="item.value">{{item.label}}</el-radio>
                                </el-radio-group>
                            </div>
                        </div>
                        <div class="operation_auto">
                            <el-checkbox v-model="leftAutomaticPrinting">自动打印</el-checkbox>
                            <el-checkbox v-model="leftAutomaticWeighing">自动过磅</el-checkbox>
                        </div>
                        <div class="operation_btn">
                            <el-button icon="el-icon-goods" type="primary" :loading="leftWeightLoading" @click="weighing('left', true)">称 重</el-button>
                            <el-button icon="el-icon-printer" type="primary" @click="printRecord">打 印</el-button>
                            <el-button icon="el-icon-files" type="primary" @click="saveSkinFun('left')" :loading="leftSaveSkinLoading">存 皮</el-button>
                            <el-button icon="el-icon-top" type="primary" @click="LpcControl('left', 'A', true)">前 起</el-button>
                            <el-button icon="el-icon-top" type="primary" @click="LpcControl('left', 'B', true)">后 起</el-button>
                            <el-button icon="el-icon-refresh" type="primary" @click="resetForm('left')">清 空</el-button>
                            <el-button icon="el-icon-bottom" type="primary" @click="LpcControl('left', 'A', false)">前 落</el-button>
                            <el-button icon="el-icon-bottom" type="primary" @click="LpcControl('left', 'B', false)">后 落</el-button>
                            <el-button icon="el-icon-switch-button" type="primary" @click="hardwareReset('left')">复 位</el-button>
                        </div>
                    </div>
                </div>

                <!-- 右侧称重区域 -->
                <div class="weight_container right-weight">
                    <div class="form_container">
                        <div class="weight_info">
                            <div class="weight_num">{{ rightWeightNum }}</div>
                            <div class="steady_state">
                                <div class="steady_state_text">单位(kg)</div>
                                <div :class="['steady_state_round', !rightSteadyState ? 'red_color' : '']"></div>
                                <div class="steady_state_text">连接</div>
                                <div :class="['steady_state_round', rightSteadyState ? 'green_color' : '']"></div>
                                <div class="steady_state_text">稳定</div>
                            </div>
                        </div>
                        <div class="weight_form">
                            <el-form :model="rightForm" label-width="100px" :rules="rules" ref="rightForm">
                                <el-row :gutter="20">
                                    <el-col :span="15">
                                        <el-form-item label="车牌号码" prop="licensePlate">
                                            <el-select @focus="getLicensePlate" v-model="rightForm.licensePlate" filterable clearable allow-create autocomplete default-first-option placeholder="请输入车牌号" @change="licensePlateCallbackFun('right', rightForm.licensePlate)">
                                                <el-option v-for="item in licensePlateOptions" :key="item.licensePlate" :label="item.licensePlate" :value="item.licensePlate">
                                                </el-option>
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="9">
                                        <el-form-item label="毛重" class="form_item">
                                            <el-input v-model="rightForm.weightM" clearable placeholder="请输入毛重" :disabled="true"></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row :gutter="20">
                                    <el-col :span="15">
                                        <el-form-item label="供应商名" prop="supplierId">
                                            <el-select @focus="getSupplier" v-model="rightForm.supplierId" filterable clearable allow-create default-first-option placeholder="请输入供应商名">
                                                <el-option v-for="item in supplierOptions" :key="item.supplierId" :label="item.supplierName" :value="item.supplierId">
                                                </el-option>
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="9">
                                        <el-form-item label="皮重" class="form_item">
                                            <el-input v-model="rightForm.weightP" placeholder="请输入皮重" clearable :disabled="true"></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row :gutter="20">
                                    <el-col :span="15">
                                        <el-form-item label="客户名称" prop="customerId">
                                            <el-select @focus="getCustomer" v-model="rightForm.customerId" filterable clearable allow-create default-first-option placeholder="请输入客户名称">
                                                <el-option v-for="item in customerOptions" :key="item.customerId" :label="item.customerName" :value="item.customerId">
                                                </el-option>
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="9">
                                        <el-form-item label="净重" class="form_item">
                                            <el-input v-model="rightForm.netWeight" clearable placeholder="请输入净重" :disabled="true"></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row :gutter="20">
                                    <el-col :span="15">
                                        <el-form-item label="货物名称" prop="materialId">
                                            <el-select @focus="getGoodsName" v-model="rightForm.materialId" @change="materialChange('right', rightForm.materialId)" filterable clearable allow-create default-first-option placeholder="请输入货物名称">
                                                <el-option v-for="item in goodsNameOptions" :key="item.materialId" :label="item.materialName" :value="item.materialId">
                                                </el-option>
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="9">
                                        <el-form-item label="扣率" prop="cutPer" class="form_item" v-if="deductionMode == 'cutPer'">
                                            <el-input type="number" min="0" max="100" v-model="rightForm.cutPer" clearable placeholder="请输入扣率"></el-input>
                                        </el-form-item>
                                        <el-form-item label="扣重" class="form_item" v-else>
                                            <el-input type="number" min="0" v-model="rightForm.cutWeight" clearable placeholder="请输入扣重"></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row :gutter="20">
                                    <el-col :span="15">
                                        <el-form-item label="规格名称">
                                            <el-select @focus="getSpecificationName(rightForm.materialId)" v-model="rightForm.specId" filterable clearable allow-create default-first-option placeholder="请输入规格名称">
                                                <el-option v-for="item in specificationNameOptions" :key="item.specId" :label="item.specName" :value="item.specId">
                                                </el-option>
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="9">
                                        <el-form-item label="备注" class="form_item">
                                            <el-input v-model="rightForm.remark" clearable placeholder="请输入备注"></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                            </el-form>
                        </div>
                    </div>
                    <div class="operation_container">
                        <div class="equipment_status">
                            <div>地感 <span :class="[rightFrontCoil != 'OFF'?'green_color': 'red_color']"></span></div>
                            <div>光栅 <span :class="[rightFrontRaster != 'OFF'?'green_color': 'red_color']"></span></div>
                            <div>地感 <span :class="[rightBackCoil != 'OFF'?'green_color': 'red_color']"></span></div>
                            <div>光栅 <span :class="[rightBackRaster != 'OFF'?'green_color': 'red_color']"></span></div>
                        </div>
                        <div class="operation_type">
                            <div class="overweight_type">
                                <div class="type_title">过磅类型:</div>
                                <el-radio-group v-model="rightForm.weightType" @change="weightTypeChange('right', rightForm.weightType, true)">
                                    <el-radio :label="item.value" size="medium" v-for="item in dict.type.business_type" :key="item.value">{{item.label}}</el-radio>
                                </el-radio-group>
                            </div>
                            <div class="overweight_type">
                                <div class="type_title">过磅模式:</div>
                                <el-radio-group :disabled="rightModelStatus" v-model="rightForm.weightMode" @change="weighingModeChange('right')">
                                    <el-radio :label="item.value" size="medium" v-for="item in dict.type.weighing_mode" :key="item.value">{{item.label}}</el-radio>
                                </el-radio-group>
                            </div>
                        </div>
                        <div class="operation_auto">
                            <el-checkbox v-model="rightAutomaticPrinting">自动打印</el-checkbox>
                            <el-checkbox v-model="rightAutomaticWeighing">自动过磅</el-checkbox>
                        </div>
                        <div class="operation_btn">
                            <el-button icon="el-icon-goods" type="primary" :loading="rightWeightLoading" @click="weighing('right', true)">称 重</el-button>
                            <el-button icon="el-icon-printer" type="primary" @click="printRecord">打 印</el-button>
                            <el-button icon="el-icon-files" type="primary" @click="saveSkinFun('right')" :loading="rightSaveSkinLoading">存 皮</el-button>
                            <el-button icon="el-icon-top" type="primary" @click="LpcControl('right', 'A', true)">前 起</el-button>
                            <el-button icon="el-icon-top" type="primary" @click="LpcControl('right', 'B', true)">后 起</el-button>
                            <el-button icon="el-icon-refresh" type="primary" @click="resetForm('right')">清 空</el-button>
                            <el-button icon="el-icon-bottom" type="primary" @click="LpcControl('right', 'A', false)">前 落</el-button>
                            <el-button icon="el-icon-bottom" type="primary" @click="LpcControl('right', 'B', false)">后 落</el-button>
                            <el-button icon="el-icon-switch-button" type="primary" @click="hardwareReset('right')">复 位</el-button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 共用的记录表格 -->
            <div class="down_body">
                <el-table :data="tableData" style="width: 100%" border :row-style="selectedStyle" @row-dblclick="rowClick">
                    <el-table-column label="磅单编码" align="center" prop="weightCode" />
                    <el-table-column label="磅单类型" align="center" prop="weightType">
                        <template slot-scope="scope">
                            <dict-tag :options="dict.type.business_type" :value="scope.row.weightType" />
                        </template>
                    </el-table-column>
                    <el-table-column label="过磅模式" align="center" prop="weightMode">
                        <template slot-scope="scope">
                            <dict-tag :options="dict.type.weighing_mode" :value="scope.row.weightMode" />
                        </template>
                    </el-table-column>
                    <el-table-column label="车牌号码" align="center" prop="licensePlate" />
                    <el-table-column label="客户名称" align="center" prop="customerName" />
                    <el-table-column label="供应商名" align="center" prop="supplierName" />
                    <el-table-column label="物料名称" align="center" prop="materialName" />
                    <el-table-column label="规格名称" align="center" prop="specName" />
                    <el-table-column label="毛重" align="center" prop="weightM" />
                    <el-table-column label="皮重" align="center" prop="weightP" />
                    <el-table-column label="净重" align="center" prop="netWeight" />
                    <el-table-column label="实重" align="center" prop="actualWeight" />
                    <el-table-column label="扣率" align="center" prop="cutPer" v-if="deductionMode == 'cutPer'" />
                    <el-table-column label="扣重" align="center" prop="cutWeight" v-if="deductionMode == 'cutWeight'" />
                    <el-table-column label="一次过磅地磅编号" align="center" prop="dbCodeFirst" />
                    <el-table-column label="二次过磅地磅编号" align="center" prop="dbCodeSecond"/>
                    <el-table-column label="一次过磅时间" align="center" prop="weighFirstTime" width="120">
                        <template slot-scope="scope">
                            <span>{{ parseTime(scope.row.weightFirstTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="二次过磅时间" align="center" prop="weighSecondTime" width="120">
                        <template slot-scope="scope">
                            <span>{{ parseTime(scope.row.weightSecondTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="过磅员" align="center" prop="weightFirstBy" />
                    <el-table-column label="磅单状态" align="center" prop="billStatus">
                        <template slot-scope="scope">
                            <dict-tag :options="dict.type.pound_status" :value="scope.row.billStatus" />
                        </template>
                    </el-table-column>
                    <el-table-column label="备注" align="center" prop="remark" />
                    <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                        <template slot-scope="scope">
                            <div style="display: flex; justify-content: flex-end; gap: 8px; flex-wrap: wrap;">
                                <el-button size="mini" type="primary" icon="el-icon-picture-outline" @click="imgViewVisibleFunction(scope.row.weightId)">图片查看</el-button>
                                <el-button size="mini" type="primary" icon="el-icon-video-camera" @click="videoViewVisibleFunction(scope.row.weightId)">视频查看</el-button>
                            </div>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </el-main>
        <el-footer class="footer">
            <div class="screening">
                <div class="screening_box">
                    <el-select v-model="query.dataType" @change="handleDataTypeChange" placeholder="请选择" class="foot_select">
                        <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"></el-option>
                    </el-select>

                    <el-select @change="handleBillStatusChange" v-model="query.billStatus" collapse-tags style="margin-left: 20px;" placeholder="请选择" class="foot_select">
                        <el-option key="item" label="全部记录" :value="undefined"></el-option>
                        <el-option v-for="item in dict.type.pound_status" :key="item.value" :label="item.label" :value="item.value"></el-option>
                    </el-select>
                    <!-- 分页组件 -->
                    <pagination
                        v-show="total > 0"
                        :total="total"
                        :page.sync="query.pageNum"
                        :limit.sync="query.pageSize"
                        @pagination="handlePagination"
                        layout="total, sizes, prev, pager, next"
                        style="background: #253e92; padding: 10px 0;"
                    />
                </div>
                <div class="cumulative">
                    <div class="video-control">
                        <el-button
                            type="primary"
                            icon="el-icon-video-camera"
                            @click="openRealtimeVideo"
                            class="realtime-video-btn">
                            实时视频
                        </el-button>
                    </div>
                    <div class="weight-summary">
                        <span class="summary-item">总车次: {{ total }}车</span>
                        <span class="summary-item">总皮重: {{ totalTareWeight }}kg</span>
                        <span class="summary-item">总毛重: {{ totalGrossWeight }}kg</span>
                        <span class="summary-item">总净重: {{ totalNetWeight }}kg</span>
                        <span class="summary-item">总实重: {{ totalActualWeight }}kg</span>
                    </div>
                </div>
            </div>
            <div class="info_box">华星智能称重管理系统 | 联系人:贺先生 | 联系电话:18674839841</div>
        </el-footer>
    </el-container>
    <img-view ref="imgView"></img-view>
    <video-view ref="videoView"></video-view>

    <!-- 实时视频弹窗 -->
    <div
        v-if="realtimeVideoVisible"
        class="realtime-video-modal"
        :style="modalStyle"
        @mousedown="startDrag"
        ref="videoModal">
        <div class="modal-header">
            <span class="modal-title">实时视频监控</span>
            <div class="modal-controls">
                <el-button
                    type="text"
                    icon="el-icon-full-screen"
                    @click="toggleFullscreen"
                    class="control-btn">
                </el-button>
                <el-button
                    type="text"
                    icon="el-icon-close"
                    @click="closeRealtimeVideo"
                    class="control-btn">
                </el-button>
            </div>
        </div>
        <div class="modal-content">
            <div class="video-container">
                <hkView></hkView>
            </div>
        </div>
        <div
            class="resize-handle"
            @mousedown="startResize">
        </div>
    </div>
</div>
</template>

<script>
import {
    listCustomer,
} from "@/api/basicInfo/customer";
import {
    updateConfig,
    listConfig
} from "@/api/system/config";
import {
    listVehicle,
} from "@/api/basicInfo/vehicle";
import {
    listSupplier,
} from "@/api/basicInfo/supplier";
import {
    listMaterial,
} from "@/api/basicInfo/material";
import {
    openSocket,
    sendMsg,
    endSocket
} from "@/utils/webSocket";
import {
    listWeightImg,
} from "@/api/weight/weightImg";
import {
    listWeightVideo,
} from "@/api/weight/weightVideo";
import {
    listSpec,
} from "@/api/basicInfo/spec";
import {
    listWeight,
    weight,
    saveSkin,
    licensePlateCallback,
} from "@/api/weight/weight";
import imgView from "@/components/imgView";
import videoView from '@/components/videoView/index.vue'
import Pagination from '@/components/Pagination'
import hkView from '@/components/hkView/index.vue'
import {
    listWeighbridge,
} from "@/api/system/weighbridge";

export default {
    name: 'doubleBalance',
    components: {
        imgView, // 图片组件
        videoView, // 视频组件
        Pagination, // 分页组件
        hkView, // 海康视频组件
    },
    dicts: ['driver_type', 'business_type', 'pound_status', 'upload_status', 'invalid_state', 'weighing_mode', 'video_address'],
    data() {
        return {
            tableData: [],
            total: 0, // 总记录数
            allTableData: [], // 存储所有数据用于总重量计算

            // 左侧称重相关数据
            leftWeightNum: 0,
            leftSteadyState: false,
            leftFrontCoil: 'ON',
            leftBackCoil: 'ON',
            leftFrontRaster: 'ON',
            leftBackRaster: 'ON',
            leftAutomaticPrinting: false,
            leftAutomaticWeighing: false,
            leftWeightLoading: false,
            leftSaveSkinLoading: false,
            leftModelStatus: false,
            leftWeightStatus: true,
            leftAutoLicense: true,
            leftRasterStatus: true,
            leftTimeWeight: null,
            leftSocketUrl: undefined,
            leftDbCode: undefined,

            // 右侧称重相关数据
            rightWeightNum: 0,
            rightSteadyState: false,
            rightFrontCoil: 'ON',
            rightBackCoil: 'ON',
            rightFrontRaster: 'ON',
            rightBackRaster: 'ON',
            rightAutomaticPrinting: false,
            rightAutomaticWeighing: false,
            rightWeightLoading: false,
            rightSaveSkinLoading: false,
            rightModelStatus: false,
            rightWeightStatus: true,
            rightAutoLicense: true,
            rightRasterStatus: true,
            rightTimeWeight: null,
            rightSocketUrl: undefined,
            rightDbCode: undefined,

            // 共用数据
            options: [{
                    value: 1,
                    label: '今日数据'
                },
                {
                    value: 2,
                    label: '昨日数据'
                },
                {
                    value: 3,
                    label: '本周数据'
                },
                {
                    value: 4,
                    label: '本月数据'
                },
                {
                    value: 5,
                    label: '本年数据'
                },
            ],

            // 左侧表单
            leftForm: {
                billStatus: undefined,
                categoryId: undefined,
                categoryName: undefined,
                customerId: undefined,
                customerName: undefined,
                dbCode: undefined,
                driverId: undefined,
                driverName: undefined,
                materialId: undefined,
                materialName: undefined,
                specId: undefined,
                specName: undefined,
                supplierId: undefined,
                supplierName: undefined,
                vehicleId: undefined,
                vehicleName: undefined,
                weight: 0,
                weightM: 0,
                weightP: 0,
                weightType: undefined,
                netWeight: 0,
                imgsPath: undefined,
                videosPath: undefined,
                licenseImgPath: undefined,
                weightMode: undefined,
                presetTare: 0,
                cutPer: undefined,
                cutWeight: undefined,
                licensePlate: undefined,
                remark: undefined,
            },

            // 右侧表单
            rightForm: {
                billStatus: undefined,
                categoryId: undefined,
                categoryName: undefined,
                customerId: undefined,
                customerName: undefined,
                dbCode: undefined,
                driverId: undefined,
                driverName: undefined,
                materialId: undefined,
                materialName: undefined,
                specId: undefined,
                specName: undefined,
                supplierId: undefined,
                supplierName: undefined,
                vehicleId: undefined,
                vehicleName: undefined,
                weight: 0,
                weightM: 0,
                weightP: 0,
                weightType: undefined,
                netWeight: 0,
                imgsPath: undefined,
                videosPath: undefined,
                licenseImgPath: undefined,
                weightMode: undefined,
                presetTare: 0,
                cutPer: undefined,
                cutWeight: undefined,
                licensePlate: undefined,
                remark: undefined,
            },

            // 表单验证规则
            rules: {
                licensePlate: [{
                    required: true,
                    message: '请输入车牌号码',
                    trigger: 'change'
                }],
                supplierId: [{
                    required: true,
                    message: '请选择供应商',
                    trigger: 'change'
                }],
                customerId: [{
                    required: true,
                    message: '请选择客户',
                    trigger: 'change'
                }],
                materialId: [{
                    required: true,
                    message: '请选择货物名称',
                    trigger: 'change'
                }],
            },

            // 选项数据
            licensePlateOptions: [],
            supplierOptions: [],
            customerOptions: [],
            goodsNameOptions: [],
            specificationNameOptions: [],

            // 其他配置
            currentRowId: undefined,
            deductionMode: 'ratio',
            hardwareServiceUrl: undefined,
            viewUrl: undefined,
            isCheckRadar: undefined,
            isCheckWeighbridge: undefined,

            // 基础信息查询参数
            basicInfoQuery: {
                pageNum: 1,
                pageSize: 9999999,
                status: 'Y',
            },

            // 表单查询参数
            query: {
                pageNum: 1,
                pageSize: 20,
                billStatus: undefined,
                dataType: 1,
                startDate: undefined,
                endDate: undefined,
            },

            // 实时视频弹窗相关
            realtimeVideoVisible: false,
            videoStreamUrl: '', // 视频流地址
            isDragging: false,
            isResizing: false,
            dragStartX: 0,
            dragStartY: 0,
            resizeStartX: 0,
            resizeStartY: 0,
            modalPosition: {
                x: 100,
                y: 100
            },
            modalSize: {
                width: 640,
                height: 480
            },
        };
    },

    watch: {
        leftAutomaticPrinting(value) {
            this.updateAutomaticConfig('automaticPrinting', value);
        },
        leftAutomaticWeighing(value) {
            this.updateAutomaticConfig('automaticWeighing', value);
        },
        rightAutomaticPrinting(value) {
            this.updateAutomaticConfig('automaticPrinting', value);
        },
        rightAutomaticWeighing(value) {
            this.updateAutomaticConfig('automaticWeighing', value);
        },
    },

    computed: {
        theme() {
            return {
                '--main-bg': this.$store.state.settings.theme,
            }
        },
        // 弹窗样式
        modalStyle() {
            return {
                left: this.modalPosition.x + 'px',
                top: this.modalPosition.y + 'px',
                width: this.modalSize.width + 'px',
                height: this.modalSize.height + 'px'
            }
        },
        // 计算总皮重
        totalTareWeight() {
            return this.allTableData.reduce((total, item) => {
                return total + (parseFloat(item.weightP) || 0);
            }, 0).toFixed(2);
        },
        // 计算总毛重
        totalGrossWeight() {
            return this.allTableData.reduce((total, item) => {
                return total + (parseFloat(item.weightM) || 0);
            }, 0).toFixed(2);
        },
        // 计算总净重
        totalNetWeight() {
            return this.allTableData.reduce((total, item) => {
                return total + (parseFloat(item.netWeight) || 0);
            }, 0).toFixed(2);
        },
        // 计算总实重
        totalActualWeight() {
            return this.allTableData.reduce((total, item) => {
                return total + (parseFloat(item.actualWeight) || 0);
            }, 0).toFixed(2);
        }
    },

    mounted() {
        this.getBasicInfo();
        this.$nextTick(() => {
            this.initConfig();
        })
    },

    methods: {
        // 打开实时视频弹窗
        openRealtimeVideo() {
            this.$showPlugin()
            this.realtimeVideoVisible = true;
            // 这里可以设置实际的视频流地址
            this.videoStreamUrl = 'http://your-video-stream-url'; // 替换为实际的视频流地址
            this.$nextTick(() => {
                // 初始化视频播放
                if (this.$refs.realtimeVideo) {
                    this.$refs.realtimeVideo.play().catch(err => {
                        console.warn('视频自动播放失败:', err);
                    });
                }
            });
        },

        // 关闭实时视频弹窗
        closeRealtimeVideo() {
            this.realtimeVideoVisible = false;
            this.$hidPlugin()
            if (this.$refs.realtimeVideo) {
                this.$refs.realtimeVideo.pause();
            }
        },

        // 切换全屏
        toggleFullscreen() {
            if (this.$refs.videoModal) {
                if (document.fullscreenElement) {
                    document.exitFullscreen();
                } else {
                    this.$refs.videoModal.requestFullscreen();
                }
            }
        },

        // 开始拖拽
        startDrag(e) {
            if (e.target.closest('.modal-header') && !e.target.closest('.modal-controls')) {
                this.isDragging = true;
                this.dragStartX = e.clientX - this.modalPosition.x;
                this.dragStartY = e.clientY - this.modalPosition.y;
                document.addEventListener('mousemove', this.onDrag);
                document.addEventListener('mouseup', this.stopDrag);
                e.preventDefault();
            }
        },

        // 拖拽中
        onDrag(e) {
            if (this.isDragging) {
                this.modalPosition.x = e.clientX - this.dragStartX;
                this.modalPosition.y = e.clientY - this.dragStartY;
            }
        },

        // 停止拖拽
        stopDrag() {
            this.isDragging = false;
            document.removeEventListener('mousemove', this.onDrag);
            document.removeEventListener('mouseup', this.stopDrag);
        },

        // 开始调整大小
        startResize(e) {
            this.isResizing = true;
            this.resizeStartX = e.clientX;
            this.resizeStartY = e.clientY;
            document.addEventListener('mousemove', this.onResize);
            document.addEventListener('mouseup', this.stopResize);
            e.preventDefault();
        },

        // 调整大小中
        onResize(e) {
            if (this.isResizing) {
                const deltaX = e.clientX - this.resizeStartX;
                const deltaY = e.clientY - this.resizeStartY;

                this.modalSize.width = Math.max(320, this.modalSize.width + deltaX);
                this.modalSize.height = Math.max(240, this.modalSize.height + deltaY);

                this.resizeStartX = e.clientX;
                this.resizeStartY = e.clientY;
            }
        },

        // 停止调整大小
        stopResize() {
            this.isResizing = false;
            document.removeEventListener('mousemove', this.onResize);
            document.removeEventListener('mouseup', this.stopResize);
        },

        // 更新自动配置
        async updateAutomaticConfig(configKey, value) {
            try {
                const response = await listConfig({ configKey });
                if (response.rows.length > 0) {
                    const data = {
                        ...response.rows[0],
                        configValue: value
                    }
                    await updateConfig(data);
                }
            } catch (error) {
                console.error('更新配置失败:', error);
            }
        },

        // 处理左侧WebSocket消息
        dealSocketMsgLeft(data) {
            this.dealSocketMsg(data, 'left');
        },

        // 处理右侧WebSocket消息
        dealSocketMsgRight(data) {
            this.dealSocketMsg(data, 'right');
        },

        // 通用WebSocket消息处理
        dealSocketMsg(data, side) {
            const isLeft = side === 'left';

            // 接收仪表值
            if (data.type >= 20000 && data.type < 30000) {
                if (isLeft) {
                    this.leftWeightNum = data.data.weight;
                    this.leftSteadyState = data.data.isStable;
                } else {
                    this.rightWeightNum = data.data.weight;
                    this.rightSteadyState = data.data.isStable;
                }

                // 实时计算净重
                const form = isLeft ? this.leftForm : this.rightForm;
                const weightType = (form.weightType || '').toString().trim();

                if (weightType === 'receive_weight' && form.weightM > 0) {
                    form.weightP = data.data.weight;
                    this.calculateNetWeight(form);
                } else if (weightType === 'ship_weight' && form.weightP > 0) {
                    form.weightM = data.data.weight;
                    this.calculateNetWeight(form);
                }

                if (data.data.isStable) {
                    form.presetTare = data.data.weight;
                    form.weight = data.data.weight;
                }
            }

            // 获取车牌
            if (data.type == 40002 || data.type == 40003) {
                const autoLicense = isLeft ? this.leftAutoLicense : this.rightAutoLicense;
                if (autoLicense) {
                    const form = isLeft ? this.leftForm : this.rightForm;
                    form.licensePlate = data.data.license;
                    this.licensePlateCallbackFun(side, form.licensePlate);
                    form.licenseColor = data.data.licenseColor;
                    form.licenseImgPath = data.data.licenseImgPath;
                    this.$forceUpdate();
                }
            }

            // 光栅信号
            if (data.type >= 50000 && data.type < 60000) {
                if (isLeft) {
                    this.leftFrontCoil = data.data.X0;
                    this.leftBackCoil = data.data.X1;
                    this.leftFrontRaster = data.data.X2;
                    this.leftBackRaster = data.data.X3;
                    this.leftRasterStatus = data.data.X2 == "ON" && data.data.X3 == "ON";
                } else {
                    this.rightFrontCoil = data.data.X0;
                    this.rightBackCoil = data.data.X1;
                    this.rightFrontRaster = data.data.X2;
                    this.rightBackRaster = data.data.X3;
                    this.rightRasterStatus = data.data.X2 == "ON" && data.data.X3 == "ON";
                }
            }
        },

        // 计算净重
        calculateNetWeight(form) {
            if (form.cutPer) {
                form.netWeight = ((Number(form.weightM) - Number(form.weightP)) - ((Number(form.weightM) - Number(form.weightP)) * Number(form.cutPer) / 100)).toFixed(0);
            } else if (form.cutWeight) {
                form.netWeight = (Number(form.weightM) - Number(form.weightP) - Number(form.cutWeight)).toFixed(0);
            } else {
                form.netWeight = (Number(form.weightM) - Number(form.weightP)).toFixed(0);
            }
        },

        // 称重方法
        async weighing(side, flag = false) {
            const isLeft = side === 'left';
            const form = isLeft ? this.leftForm : this.rightForm;
            const steadyState = isLeft ? this.leftSteadyState : this.rightSteadyState;
            const rasterStatus = isLeft ? this.leftRasterStatus : this.rightRasterStatus;
            const weightStatus = isLeft ? this.leftWeightStatus : this.rightWeightStatus;

            if (!form.licensePlate) {
                this.$message.error('请输入车牌号');
                return;
            }

            if (form.weightType == 'receive_weight') {
                if (!form.supplierId) {
                    this.$message.error('请输入供应商');
                    return;
                }
            } else {
                if (!form.customerId) {
                    this.$message.error('请输入客户');
                    return;
                }
            }

            if (!form.materialId) {
                this.$message.error('请输入物料');
                return;
            }

            if (!steadyState) {
                this.$message.error('请等待称重稳定');
                return;
            }

            if (!rasterStatus && this.isCheckRadar == 'Y') {
                this.$message.error('请停适当位置');
                return;
            }

            if (!weightStatus && this.isCheckWeighbridge == 'Y') {
                this.$message.error('请先下磅再称重');
                return;
            }

            if (isLeft) {
                this.leftWeightLoading = true;
            } else {
                this.rightWeightLoading = true;
            }

            if (flag) {
                const data = await this.takePicture();
                form.imagesPath = data.data.filePath;
            }

            form.dbCode = isLeft ? this.leftDbCode : this.rightDbCode;
            this.submitForm(side);
        },

        // 表单提交
        async submitForm(side) {
            const isLeft = side === 'left';
            const form = isLeft ? this.leftForm : this.rightForm;

            // 处理自定义输入的非ID字段
            const fields = ['supplierId', 'customerId', 'materialId', 'specId'];
            fields.forEach(field => {
                if (form[field] && !/^\d+$/.test(form[field])) {
                    form[field.replace('Id', 'Name')] = form[field];
                    form[field] = undefined;
                }
            });

            try {
                const res = await weight(form);
                if (res.code === 200) {
                    this.$message.success('称重成功');

                    if (isLeft) {
                        this.leftWeightStatus = false;
                    } else {
                        this.rightWeightStatus = false;
                    }

                    this.resetForm(side);
                    this.getBasicInfo();

                    // 道闸控制
                    if (form.inOut == 'A') {
                        this.LpcControl(side, 'B', true);
                    } else {
                        this.LpcControl(side, 'A', true);
                    }

                    // 语音播报和大屏幕显示
                    this.voiceBroadcast(res.data);
                    this.showBigScreen(res.data);

                    // 自动打印
                    const automaticPrinting = isLeft ? this.leftAutomaticPrinting : this.rightAutomaticPrinting;
                    if (automaticPrinting && res.data.billStatus == "weighted") {
                        this.printRecord(res.data.weightId);
                    }
                } else {
                    this.$message.error(res.msg || '称重失败');
                }
            } catch (error) {
                this.$message.error('称重请求失败');
                console.error('称重错误:', error);
            } finally {
                if (isLeft) {
                    this.leftWeightLoading = false;
                } else {
                    this.rightWeightLoading = false;
                }
            }
        },

        // 存皮方法
        async saveSkinFun(side) {
            const isLeft = side === 'left';
            const form = isLeft ? this.leftForm : this.rightForm;
            const steadyState = isLeft ? this.leftSteadyState : this.rightSteadyState;

            if (!form.licensePlate) {
                this.$message.error('请输入车牌号');
                return;
            }

            if (!steadyState) {
                this.$message.error('请等待称重稳定');
                return;
            }

            if (isLeft) {
                this.leftSaveSkinLoading = true;
            } else {
                this.rightSaveSkinLoading = true;
            }

            try {
                const res = await saveSkin(form);
                if (res.code == 200) {
                    this.$message.success('存皮成功');
                    this.resetForm(side);
                } else {
                    this.$message.error(res.msg || '存皮失败');
                }
            } catch (error) {
                this.$message.error('存皮请求失败');
                console.error('存皮错误:', error);
            } finally {
                if (isLeft) {
                    this.leftSaveSkinLoading = false;
                } else {
                    this.rightSaveSkinLoading = false;
                }
            }
        },

        // 清空表单
        resetForm(side) {
            const isLeft = side === 'left';
            const form = isLeft ? this.leftForm : this.rightForm;

            form.licensePlate = undefined;
            form.materialId = undefined;
            form.materialName = undefined;
            form.supplierId = undefined;
            form.supplierName = undefined;
            form.customerId = undefined;
            form.customerName = undefined;
            form.specId = undefined;
            form.specName = undefined;
            form.weight = 0;
            form.weightM = 0;
            form.weightP = 0;
            form.netWeight = 0;
            form.weightNum = 0;
            form.cutPer = undefined;
            form.cutWeight = undefined;
            form.imgsPath = undefined;
            form.videosPath = undefined;
            form.licenseColor = undefined;
            form.licenseImgPath = undefined;
            form.remark = undefined;
            form.presetTare = 0;

            if (isLeft) {
                this.leftModelStatus = false;
            } else {
                this.rightModelStatus = false;
            }
        },

        // 车牌回调接口
        async licensePlateCallbackFun(side, licensePlate) {
            if (!licensePlate) {
                this.resetForm(side);
                return;
            }

            const isLeft = side === 'left';
            const form = isLeft ? this.leftForm : this.rightForm;

            try {
                const res = await licensePlateCallback({ licensePlate });
                if (res.data.length > 0) {
                    if (isLeft) {
                        this.leftModelStatus = true;
                    } else {
                        this.rightModelStatus = true;
                    }

                    const info = res.data[0];
                    form.materialId = info.materialId;
                    form.supplierId = info.supplierId;
                    form.customerId = info.customerId;
                    form.specId = info.specId;
                    form.weightM = info.weightM;
                    form.weightP = info.weightP;
                    form.netWeight = info.netWeight;
                    form.cutPer = info.cutPer;
                    form.cutWeight = info.cutWeight;
                    form.remark = info.remark;
                    form.weightType = info.weightType;
                    form.weightMode = info.weightMode;

                    this.weightTypeChange(side, form.weightType, false);
                    this.$forceUpdate();
                } else {
                    if (isLeft) {
                        this.leftModelStatus = false;
                    } else {
                        this.rightModelStatus = false;
                    }
                }
            } catch (error) {
                console.error('车牌回调接口错误:', error);
            }
        },

        // 模式切换
        weighingModeChange(side) {
            const isLeft = side === 'left';
            const form = isLeft ? this.leftForm : this.rightForm;

            listConfig({ configKey: 'weighingMode' }).then(res => {
                if (res.rows.length > 0) {
                    const data = {
                        ...res.rows[0],
                        configValue: form.weightMode
                    };
                    this.updateConfig(data);
                }
            });
        },

        // 业务切换
        weightTypeChange(side, value, flag = false) {
            listConfig({ configKey: 'weightType' }).then(res => {
                if (res.rows.length > 0) {
                    const data = {
                        ...res.rows[0],
                        configValue: value
                    };
                    this.updateConfig(data);
                }
            });

            if (flag) {
                const isLeft = side === 'left';
                const form = isLeft ? this.leftForm : this.rightForm;
                this.licensePlateCallbackFun(side, form.licensePlate);
            }

            if (value == 'receive_weight') {
                this.rules.customerId[0].required = false;
                this.rules.supplierId[0].required = true;
            } else {
                this.rules.customerId[0].required = true;
                this.rules.supplierId[0].required = false;
            }
        },

        // 物料选择
        materialChange(side, value) {
            const isLeft = side === 'left';
            const form = isLeft ? this.leftForm : this.rightForm;
            form.specId = undefined;
            this.getSpecificationName(value);
        },

        // 初始化参数
        async initConfig() {
            const that = this;

            // 获取称重类型
            const weightType = await this.fetchConfigByKey('weightType');
            this.leftForm.weightType = weightType.msg;
            this.rightForm.weightType = weightType.msg;
            this.weightTypeChange('left', weightType.msg);
            this.weightTypeChange('right', weightType.msg);

            // 获取自动打印
            const automaticPrinting = await this.fetchConfigByKey('automaticPrinting');
            this.leftAutomaticPrinting = automaticPrinting.msg == 'true';
            this.rightAutomaticPrinting = automaticPrinting.msg == 'true';

            // 获取自动称重
            const automaticWeighing = await this.fetchConfigByKey('automaticWeighing');
            this.leftAutomaticWeighing = automaticWeighing.msg == 'true';
            this.rightAutomaticWeighing = automaticWeighing.msg == 'true';

            // 获取地磅编号 - 左右两个不同的地磅
            const leftDbCode = await this.fetchConfigByKey('leftDbCode');
            const rightDbCode = await this.fetchConfigByKey('rightDbCode');
            this.leftDbCode = leftDbCode.msg;
            this.rightDbCode = rightDbCode.msg;

            // 扣杂计算方式
            const deductionMode = await this.fetchConfigByKey('deduction_algorithm');
            this.deductionMode = deductionMode.msg;

            // 获取过磅模式
            const weighingMode = await this.fetchConfigByKey('weighingMode');
            this.leftForm.weightMode = weighingMode.msg;
            this.rightForm.weightMode = weighingMode.msg;

            // 获取左侧websocket地址
            const leftWeighbridgeData = await listWeighbridge({
                weighbridgeCode: this.leftDbCode
            });
            if (leftWeighbridgeData.rows.length > 0) {
                this.leftSocketUrl = leftWeighbridgeData.rows[0].wsUrl;
                openSocket(
                    this.leftSocketUrl,
                    function (msg) {
                        let data = JSON.parse(msg.data);
                        that.dealSocketMsgLeft(data);
                    }, 3000
                );
            }

            // 获取右侧websocket地址
            const rightWeighbridgeData = await listWeighbridge({
                weighbridgeCode: this.rightDbCode
            });
            if (rightWeighbridgeData.rows.length > 0) {
                this.rightSocketUrl = rightWeighbridgeData.rows[0].wsUrl;
                openSocket(
                    this.rightSocketUrl,
                    function (msg) {
                        let data = JSON.parse(msg.data);
                        that.dealSocketMsgRight(data);
                    }, 3000
                );
            }

            // 硬件服务地址
            const hardwareServiceUrl = await this.fetchConfigByKey('hardware_address');
            this.hardwareServiceUrl = hardwareServiceUrl.msg;

            // 视图查看路径
            const viewUrl = await this.fetchConfigByKey('view_preview');
            this.viewUrl = viewUrl.msg;

            // 是否检测光栅
            const isCheckRadar = await this.fetchConfigByKey('grating_detection');
            this.isCheckRadar = isCheckRadar.msg;

            // 是否检测下磅
            const isCheckWeighbridge = await this.fetchConfigByKey('lost_weigh');
            this.isCheckWeighbridge = isCheckWeighbridge.msg;
        },

        // 修改参数
        async updateConfig(data) {
            await updateConfig(data);
        },

        // 参数查询方法封装
        fetchConfigByKey(key) {
            return new Promise((resolve, reject) => {
                this.getConfigKey(key)
                    .then(response => {
                        resolve(response);
                    })
                    .catch(error => {
                        reject(error);
                    });
            });
        },

        // 基础信息查询
        getBasicInfo() {
            this.getCustomer();
            this.getSupplier();
            this.getLicensePlate();
            this.getGoodsName();
            this.getWeighbridgeList();
            this.getSpecificationName();
        },

        // 客户数据查询
        async getCustomer() {
            try {
                const response = await listCustomer(this.basicInfoQuery);
                this.customerOptions = response.rows;
            } catch (error) {
                this.$message.error(error.msg);
            }
        },

        // 供应商数据查询
        async getSupplier() {
            try {
                const response = await listSupplier(this.basicInfoQuery);
                this.supplierOptions = response.rows;
            } catch (error) {
                this.$message.error(error.msg);
            }
        },

        // 货物名称数据查询
        async getGoodsName() {
            try {
                const response = await listMaterial(this.basicInfoQuery);
                this.goodsNameOptions = response.rows;
            } catch (error) {
                this.$message.error(error.msg);
            }
        },

        // 规格名称数据查询
        async getSpecificationName(materialId) {
            let id = undefined;
            const IsNum = /^\d+$/;

            if (IsNum.test(materialId)) {
                id = materialId;
            }

            try {
                const response = await listSpec({
                    materialId: id,
                    ...this.basicInfoQuery
                });
                this.specificationNameOptions = response.rows;
            } catch (error) {
                this.$message.error(error.msg);
            }
        },

        // 车牌号码数据查询
        async getLicensePlate() {
            try {
                const response = await listVehicle(this.basicInfoQuery);
                this.licensePlateOptions = response.rows;
            } catch (error) {
                this.$message.error(error.msg);
            }
        },

        // 称重列表查询
        async getWeighbridgeList() {
            try {
                this.setQueryTimeRange();
                const response = await listWeight(this.query);
                this.tableData = response.rows || [];
                this.allTableData = response.rows || [];
                this.total = response.total || 0;
            } catch (error) {
                this.$message.error(error.msg);
            }
        },

        // 设置查询时间范围
        setQueryTimeRange() {
            const now = new Date();

            switch (this.query.dataType) {
                case 1: // 今天
                    const today = new Date(now);
                    today.setHours(0, 0, 0, 0);
                    const tomorrow = new Date(today);
                    tomorrow.setDate(today.getDate() + 1);
                    this.query.startDate = this.formatDateTime(today);
                    this.query.endDate = this.formatDateTime(tomorrow);
                    break;
                case 2: // 昨天
                    const yesterday = new Date(now);
                    yesterday.setDate(now.getDate() - 1);
                    yesterday.setHours(0, 0, 0, 0);
                    const todayEnd = new Date(yesterday);
                    todayEnd.setDate(yesterday.getDate() + 1);
                    this.query.startDate = this.formatDateTime(yesterday);
                    this.query.endDate = this.formatDateTime(todayEnd);
                    break;
                case 3: // 本周
                    const startOfWeek = new Date(now);
                    const day = startOfWeek.getDay();
                    const diff = startOfWeek.getDate() - day + (day === 0 ? -6 : 1);
                    startOfWeek.setDate(diff);
                    startOfWeek.setHours(0, 0, 0, 0);
                    const endOfWeek = new Date(startOfWeek);
                    endOfWeek.setDate(startOfWeek.getDate() + 6);
                    endOfWeek.setHours(23, 59, 59, 999);
                    this.query.startDate = this.formatDateTime(startOfWeek);
                    this.query.endDate = this.formatDateTime(endOfWeek);
                    break;
                case 4: // 本月
                    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
                    const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);
                    endOfMonth.setHours(23, 59, 59, 999);
                    this.query.startDate = this.formatDateTime(startOfMonth);
                    this.query.endDate = this.formatDateTime(endOfMonth);
                    break;
                case 5: // 本年
                    const startOfYear = new Date(now.getFullYear(), 0, 1);
                    const endOfYear = new Date(now.getFullYear(), 11, 31);
                    endOfYear.setHours(23, 59, 59, 999);
                    this.query.startDate = this.formatDateTime(startOfYear);
                    this.query.endDate = this.formatDateTime(endOfYear);
                    break;
                default:
                    this.query.startDate = undefined;
                    this.query.endDate = undefined;
                    break;
            }
        },

        // 格式化日期时间
        formatDateTime(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            const seconds = String(date.getSeconds()).padStart(2, '0');
            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        },

        // 分页处理方法
        handlePagination(pagination) {
            this.query.pageNum = pagination.page;
            this.query.pageSize = pagination.limit;
            this.getWeighbridgeList();
        },

        // 数据类型改变处理
        handleDataTypeChange() {
            this.query.pageNum = 1;
            this.getWeighbridgeList();
        },

        // 磅单状态改变处理
        handleBillStatusChange() {
            this.query.pageNum = 1;
            this.getWeighbridgeList();
        },

        // 记录打印
        async printRecord(id) {
            if (!this.hardwareServiceUrl) {
                this.$message.warning('请先配置硬件服务地址');
                return;
            }

            const url = this.hardwareServiceUrl + '/api/hardware/v3/activeX/printFile';
            const Id = this.currentRowId || id;
            if (!Id) {
                this.$message.warning('请先选择一条数据');
                return;
            }

            try {
                const res = await this.$axios.post(url, { data: Id });
                const result = JSON.parse(res.data.result);
                if (result.code == 0) {
                    this.$message.success('打印成功');
                } else {
                    this.$message.error('打印失败');
                }
            } catch (error) {
                console.error('打印错误:', error);
            }
        },

        // 道闸控制
        LpcControl(side, name, value) {
            if (!this.hardwareServiceUrl) {
                this.$message.warning('请先配置硬件服务地址');
                return;
            }

            const url = this.hardwareServiceUrl + '/api/hardware/v1/deviceOp/switchGate';
            console.log(`${side}侧道闸操作 ==》 道闸：${name}，动作：${value}`);

            this.$axios.post(url, {
                enter: name,
                signal: value,
            }).then((res) => {
                if (res.data.code == 500) {
                    console.log(res.data.msg);
                }
            }).catch((res) => {
                console.error('道闸控制失败:', res);
            });
        },

        // 大屏幕显示
        async showBigScreen(result) {
            if (!this.hardwareServiceUrl) {
                this.$message.warning('请先配置硬件服务地址');
                return;
            }

            const url = this.hardwareServiceUrl + '/api/hardware/v3/screen/showJlgScreen';
            const year = new Date().getFullYear();
            const month = new Date().getMonth() + 1;
            const day = new Date().getDate();

            let str;
            if (result.billStatus == "weighted") {
                str = `${result.licensePlate}，一次过磅${result.weightFirst}Kg，二次过磅${result.weightSecond}Kg，净重:${result.netWeight}Kg，${year}-${month}-${day}`;
            } else {
                str = `${result.licensePlate}，重量:${this.leftWeightNum || this.rightWeightNum}Kg，${year}-${month}-${day}`;
            }

            const BigScreenObj = {
                deviceName: "大屏幕",
                text: str,
            };

            try {
                const { data } = await this.$axios.post(url, BigScreenObj);
                console.log('大屏幕显示结果:', data);
                if (data.code === 500) {
                    console.error('大屏幕显示错误:', data.msg);
                    this.$message.error('大屏幕显示失败');
                }
            } catch (error) {
                console.error('大屏幕显示异常:', error);
                this.$message.error('大屏幕显示服务异常');
            }
        },

        // 统一抓拍
        async takePicture() {
            if (!this.hardwareServiceUrl) {
                this.$message.warning('请先配置硬件服务地址');
                return Promise.resolve({ data: { data: { filePath: '' } } });
            }

            const url = this.hardwareServiceUrl + '/api/hardware/v3/carmera/takePhotoAll/';
            try {
                const { data } = await this.$axios.post(url);
                const result = JSON.parse(data.result);
                console.log('抓拍结果:', result);

                if (result.code === 500) {
                    console.error('抓拍错误:', result.message);
                    this.$message.error('抓拍失败');
                    return Promise.reject(result.message);
                }

                if (!result.data) {
                    return Promise.resolve({ data: { data: { filePath: '' } } });
                } else {
                    return Promise.resolve(result);
                }
            } catch (error) {
                console.error('抓拍异常:', error);
                this.$message.error('抓拍服务异常');
                return Promise.resolve({ data: { data: { filePath: '' } } });
            }
        },

        // 语音播报
        async voiceBroadcast(result) {
            if (!this.hardwareServiceUrl) {
                this.$message.warning('请先配置硬件服务地址');
                return;
            }

            const url = this.hardwareServiceUrl + '/api/hardware/v3/activeX/voicePlay';
            let str = `称重完成，车辆请下磅`;

            if (result.weightType == "weighted") {
                str = `称重完成，车牌${result.licensePlate}，一次过磅${result.weightFirst}千克，二次过磅${result.weightSecond}千克，净重${result.netWeight}千克，车辆请下磅`;
            } else {
                str = `称重完成，车牌${result.licensePlate}，重量${this.leftWeightNum || this.rightWeightNum}千克，车辆请下磅`;
            }

            let voiceObj = {
                "text": str,
                "speed": 45,
                "volume": 100,
            };

            try {
                const { data } = await this.$axios.post(url, voiceObj);
                console.log('语音播报结果:', data);
                if (data.code === 500) {
                    console.error('语音播报错误:', data.msg);
                    this.$message.error('语音播报失败');
                }
            } catch (error) {
                console.error('语音播报异常:', error);
                this.$message.error('语音播报服务异常');
            }
        },

        // 图片查看
        imgViewVisibleFunction(id) {
            const RowId = this.currentRowId || id;
            if (!RowId) {
                this.$message.warning('请先选择一条数据');
                return;
            }

            const urlList = [];
            listWeightImg({ weightId: RowId }).then(response => {
                if (response.rows.length == 0) {
                    this.$message.warning('暂无图片');
                    return;
                }
                response.rows.forEach(item => {
                    urlList.push(item.weightImgPath);
                });
                this.$refs.imgView.openDialog(urlList, this.viewUrl);
            });
        },

        // 视频查看
        videoViewVisibleFunction(id) {
            const RowId = this.currentRowId || id;
            if (!RowId) {
                this.$message.warning('请先选择一条数据');
                return;
            }

            listWeightVideo({ weightId: RowId }).then(response => {
                if (response.rows.length == 0) {
                    this.$message.warning('暂无视频');
                    return;
                }
                const urlList = response.rows.map(item => item.weightVideoPath);
                this.$refs.videoView.openDialog(urlList, this.viewUrl);
            });
        },

        // 硬件复位
        async hardwareReset(side) {
            if (!this.hardwareServiceUrl) {
                this.$message.warning('请先配置硬件服务地址');
                return;
            }

            const url = this.hardwareServiceUrl + '/api/hardware/v1/deviceOp/reset';

            try {
                const { data } = await this.$axios.post(url, {});
                console.log(`${side}侧硬件复位结果:`, data);
                const result = JSON.parse(data.result);

                if (result.code == 0) {
                    const isLeft = side === 'left';
                    if (isLeft) {
                        this.leftAutoLicense = true;
                        clearInterval(this.leftTimeWeight);
                        this.leftTimeWeight = null;
                        this.leftWeightStatus = true;
                    } else {
                        this.rightAutoLicense = true;
                        clearInterval(this.rightTimeWeight);
                        this.rightTimeWeight = null;
                        this.rightWeightStatus = true;
                    }
                    this.$message.success(`${side}侧硬件复位成功`);
                }
            } catch (error) {
                console.error(`${side}侧硬件复位异常:`, error);
                this.$message.error(`${side}侧硬件复位服务异常`);
            }
        },

        // 选择某一行改变颜色确定选中行
        selectedStyle({ row, rowIndex }) {
            if (this.currentRowId == row.weightId) {
                return {
                    "background-color": '#1890ff',
                    "color": '#fff'
                };
            }
        },

        // 表格行点击事件
        rowClick(row, column, event) {
            this.currentRowId = row.weightId;
            // 可以选择将数据填充到左侧或右侧表单
            // 这里默认填充到左侧表单
            this.leftForm = {
                ...this.leftForm,
                ...row,
            };
            this.leftForm.netWeight = 0;
            this.leftForm.weightM = 0;
            this.leftForm.weightP = 0;
            this.licensePlateCallbackFun('left', this.leftForm.licensePlate);
        },

        // 获取配置项的通用方法
        getConfigKey(key) {
            return listConfig({ configKey: key }).then(response => {
                if (response.rows && response.rows.length > 0) {
                    return { msg: response.rows[0].configValue };
                }
                return { msg: '' };
            });
        },
    },

    beforeDestroy() {
        // 清理WebSocket连接和定时器
        if (this.leftTimeWeight) {
            clearInterval(this.leftTimeWeight);
            this.leftTimeWeight = null;
        }
        if (this.rightTimeWeight) {
            clearInterval(this.rightTimeWeight);
            this.rightTimeWeight = null;
        }
        endSocket();
    },
};
</script>

<style lang="scss" scoped>
@font-face {
    font-family: electronicFont;
    src: url(../../../assets/font/DS-DIGIT.TTF);
}

.doubleBalance {
    ::v-deep .btn-prev{
        margin-left: 50px;
    }
    .el-main {
        height: calc(100vh - 110px);
        background-color: #0e5495 !important;
        padding: 0;
        padding-top: 10px;

        .red_color {
            background-color: red !important;
            width: 25px;
            height: 25px;
            border-radius: 50%;
            display: inline-block;
        }

        .green_color {
            background-color: rgb(17, 194, 17) !important;
            width: 25px;
            height: 25px;
            border-radius: 50%;
            display: inline-block;
        }

        .top_body {
            display: flex;
            height: 60%;
            gap: 10px;

            .weight_container {
                flex: 1;
                display: flex;
                border: 2px solid #1890ff;
                border-radius: 10px;
                background: rgba(24, 144, 255, 0.1);

                &.left-weight {
                    border-color: #52c41a;
                    background: rgba(82, 196, 26, 0.1);
                }

                &.right-weight {
                    border-color: #fa8c16;
                    background: rgba(250, 140, 22, 0.1);
                }

                .form_container {
                    flex: 1;
                    display: flex;
                    flex-wrap: wrap;
                    align-items: center;
                    justify-content: center;

                    .weight_info {
                        width: 100%;

                        .weight_num {
                            width: 80%;
                            margin: 0 auto;
                            font-size: 80px;
                            background-color: #1890ff;
                            color: #fff;
                            height: 100px;
                            border-radius: 10px;
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            font-family: electronicFont;
                        }

                        .steady_state {
                            width: 100%;
                            height: 35px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            color: #fff;

                            div {
                                margin: 0 5px;
                                font-size: 16px;
                            }

                            .steady_state_round {
                                width: 20px;
                                height: 20px;
                                border-radius: 50%;
                                background-color: #7f7f7f;
                                display: inline-block;
                            }
                        }
                    }

                    .weight_form {
                        margin-top: 10px;
                        width: 100%;

                        ::v-deep .el-input__inner {
                            height: 40px !important;
                            font-size: 16px;
                            border-color: #e5e7eb;
                        }

                        ::v-deep.el-select {
                            width: 100%;
                        }

                        ::v-deep .el-form-item__label {
                            font-size: 16px;
                            line-height: 38px;
                            color: #fff;
                        }

                        ::v-deep .el-form-item {
                            margin-bottom: 15px;
                        }

                        ::v-deep .el-form-item__error {
                            display: none;
                        }

                        .form_item {
                            box-sizing: border-box;

                            ::v-deep .el-form-item__label {
                                width: 56px !important;
                            }

                            ::v-deep .el-form-item__content {
                                margin-left: 56px !important;
                            }
                        }
                    }
                }

                .operation_container {
                    width: 30%;
                    height: 100%;
                    display: flex;
                    flex-wrap: wrap;
                    align-items: center;
                    justify-content: center;

                    .equipment_status {
                        width: 90%;
                        height: 80px;
                        border: #b7b7be solid 1px;
                        display: flex;
                        flex-wrap: wrap;
                        justify-content: space-evenly;
                        border-radius: 5px;

                        div {
                            width: 50%;
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            font-size: 18px;
                            color: #fff;

                            span {
                                margin-left: 10px;
                            }
                        }
                    }

                    .operation_type {
                        display: flex;
                        width: 90%;
                        justify-content: space-between;

                        .overweight_type {
                            width: 48%;
                            border: #b7b7be solid 1px;
                            border-radius: 5px;
                            box-sizing: border-box;
                            padding: 5px;

                            .type_title {
                                font-size: 1.041667vw;
                                font-weight: 600;
                                color: #fff;
                            }

                            ::v-deep.el-radio {
                                height: 25px;
                                display: flex;
                                align-items: center;
                            }

                            ::v-deep.el-radio-group {
                                display: flex;
                                flex-direction: column;
                                margin-left: 10px;
                            }

                            ::v-deep.el-radio__label {
                                font-size: 0.85vw;
                                font-weight: bold;
                                color: #fff;
                            }
                        }
                    }

                    .operation_auto {
                        width: 90%;
                        border: #b7b7be solid 1px;
                        border-radius: 5px;
                        box-sizing: border-box;
                        padding: 8px;
                        display: flex;
                        justify-content: space-evenly;
                        align-items: center;

                        ::v-deep.el-checkbox__label {
                            font-size: 0.9375vw;
                            font-weight: bold;
                            color: #fff;
                        }

                        ::v-deep.el-checkbox__inner {
                            height: 14px;
                            width: 14px;
                        }

                        ::v-deep.el-checkbox__inner::after {
                            left: 4px;
                            top: 1px;
                        }
                    }

                    .operation_btn {
                        width: 90%;
                        display: flex;
                        flex-wrap: wrap;
                        justify-content: space-between;
                        align-items: center;

                        ::v-deep.el-button {
                            margin: 0;
                            margin-top: 8px;
                            width: calc(33.3% - 5px);
                            font-size: 0.9375vw;
                            padding: 0.520833vw 0.520833vw;
                        }
                    }
                }
            }
        }

        .down_body {
            background: #253e92;
            height: 40%;
            overflow: auto;

            ::v-deep.cell {
                font-size: 16px;
            }

            ::v-deep.el-table__header-wrapper th {
                background: #253e92;
                color: #fff;
                font-size: 16px;
                font-weight: 600;
            }

            ::v-deep.el-table__body tr:hover>td {
                background-color: #1890ff !important;
                color: #fff;
            }

            ::v-deep.el-table__empty-block {
                background: #253e92;
            }

            ::v-deep.el-table__row {
                background-color: #253e92;
            }

            ::v-deep.el-table__cell {
                color: #fff;
            }
        }
    }

    .footer {
        background: #253e92;
        padding: 0;
        font-size: 16px;

        ::v-deep.el-select {
            margin-left: 10px;
        }

        .screening {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 20px;
            height: 30px;

            .screening_box {
                display: flex;
                align-items: center;

                ::v-deep .el-input__inner {
                    background: #e2e6eb !important;
                    border-color: #000 !important;
                    height: 30px;
                    width: 150px;
                    line-height: 30px;
                    color: black;
                }

                ::v-deep.el-input__inner::placeholder {
                    color: black;
                }

                ::v-deep.el-input__icon {
                    line-height: 30px;
                    color: black;
                }

                ::v-deep .el-button {
                    width: 150px;
                    height: 25px !important;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    margin-left: 20px;
                }
            }

            .cumulative {
                display: flex;
                align-items: center;
                gap: 20px;

                .video-control {
                    .realtime-video-btn {
                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                        border: none;
                        color: white;
                        font-weight: bold;
                        border-radius: 6px;
                        padding: 8px 16px;
                        transition: all 0.3s ease;

                        &:hover {
                            transform: translateY(-2px);
                            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
                        }

                        &:active {
                            transform: translateY(0);
                        }
                    }
                }

                .weight-summary {
                    display: flex;
                    gap: 20px;

                    .summary-item {
                        color: #fff;
                        font-size: 14px;
                        font-weight: bold;
                        background: rgba(255, 255, 255, 0.1);
                        padding: 2px 10px;
                        border-radius: 4px;
                        border: 1px solid rgba(255, 255, 255, 0.3);
                    }
                }
            }
        }

        .info_box {
            background-color: #1890ff;
            height: 30px;
            color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 16px;
        }

        // 分页组件样式
        ::v-deep .pagination-container {
            margin-top: 0px !important;
            background: transparent !important;

            .el-pagination {
                .el-pagination__total,
                .el-pagination__jump,
                .el-pager li,
                .el-pagination__sizes .el-input__inner,
                .btn-prev,
                .btn-next {
                    color: #fff !important;
                    background: transparent !important;
                    border-color: rgba(255, 255, 255, 0.3) !important;
                }

                .el-pager li:hover,
                .el-pager li.active {
                    background: #1890ff !important;
                    color: #fff !important;
                }

                .btn-prev:hover,
                .btn-next:hover {
                    background: rgba(255, 255, 255, 0.1) !important;
                }

                .el-pagination__sizes .el-input .el-input__inner {
                    background: rgba(255, 255, 255, 0.1) !important;
                    border-color: rgba(255, 255, 255, 0.3) !important;
                    color: #fff !important;
                }
            }
        }
    }
}

/* 实时视频弹窗样式 */
.realtime-video-modal {
    position: fixed;
    z-index: 9999;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    overflow: hidden;
    min-width: 320px;
    min-height: 240px;
    user-select: none;

    .modal-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 12px 16px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        cursor: move;

        .modal-title {
            font-weight: bold;
            font-size: 14px;
        }

        .modal-controls {
            display: flex;
            gap: 8px;

            .control-btn {
                color: white !important;
                padding: 4px !important;
                min-height: auto !important;
                border: none !important;
                background: transparent !important;

                &:hover {
                    background: rgba(255, 255, 255, 0.2) !important;
                    border-radius: 4px;
                }

                i {
                    font-size: 16px;
                }
            }
        }
    }

    .modal-content {
        height: calc(100% - 48px);
        padding: 0;

        .video-container {
            width: 100%;
            height: 100%;
            background: #000;
            display: flex;
            align-items: center;
            justify-content: center;

            .video-player {
                width: 100%;
                height: 100%;
                object-fit: contain;
            }
        }
    }

    .resize-handle {
        position: absolute;
        bottom: 0;
        right: 0;
        width: 20px;
        height: 20px;
        cursor: nw-resize;
        background: linear-gradient(-45deg, transparent 0%, transparent 40%, #ccc 40%, #ccc 60%, transparent 60%);

        &:hover {
            background: linear-gradient(-45deg, transparent 0%, transparent 40%, #999 40%, #999 60%, transparent 60%);
        }
    }

    /* 全屏状态下的样式调整 */
    &:fullscreen {
        .modal-header {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            z-index: 10;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        &:hover .modal-header {
            opacity: 1;
        }

        .modal-content {
            height: 100%;
        }

        .resize-handle {
            display: none;
        }
    }
}
</style>
