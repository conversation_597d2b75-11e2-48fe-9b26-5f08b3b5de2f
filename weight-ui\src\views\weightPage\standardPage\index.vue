<template>
<div class="weight">
    <el-container>
        <el-main class="el-main" :style="theme">
            <div :span="16" class="top_body">
                <div class="weight_container">
                    <div class="form_container">
                        <div class="weight_info">
                            <div class="weight_num">{{ weightNum }}</div>
                            <div class="steady_state">
                                <div class="steady_state_text">单位(kg)</div>
                                <div :class="['steady_state_round', !steadyState ? 'red_color' : '']"></div>
                                <div class="steady_state_text">连接</div>
                                <div :class="['steady_state_round', steadyState ? 'green_color' : '']"></div>
                                <div class="steady_state_text">稳定</div>
                            </div>
                        </div>
                        <div class="weight_form">
                            <el-form :model="form" label-width="100px" :rules="rules" ref="form">
                                <el-row :gutter="20">
                                    <el-col :span="15">
                                        <el-form-item label="车牌号码" prop="licensePlate">
                                            <el-select id="licensePlate" @focus="getLicensePlate" v-model="form.licensePlate" filterable clearable allow-create autocomplete default-first-option placeholder="请输入车牌号" @change="licensePlateCallbackFun">
                                                <el-option v-for="item in licensePlateOptions" :key="item.licensePlate" :label="item.licensePlate" :value="item.licensePlate">
                                                </el-option>
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="9">
                                        <el-form-item label="毛重" class="form_item">
                                            <el-input id="grossWeight" v-model="form.weightM" clearable placeholder="请输入毛重" :disabled="true"></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row :gutter="20">
                                    <el-col :span="15">
                                        <el-form-item label="供应商名" prop="supplierId">
                                            <el-select id="supplier" @focus="getSupplier" v-model="form.supplierId" filterable clearable allow-create default-first-option placeholder="请输入供应商名">
                                                <el-option v-for="item in supplierOptions" :key="item.supplierId" :label="item.supplierName" :value="item.supplierId">
                                                </el-option>
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="9">
                                        <el-form-item id="tareWeight" label="皮重" class="form_item">
                                            <el-input v-model="form.weightP" placeholder="请输入皮重" clearable :disabled="true"></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row :gutter="20">
                                    <el-col :span="15">
                                        <el-form-item label="客户名称" prop="customerId">
                                            <el-select id="customer" @focus="getCustomer" v-model="form.customerId" filterable clearable allow-create default-first-option placeholder="请输入客户名称">
                                                <el-option v-for="item in customerOptions" :key="item.customerId" :label="item.customerName" :value="item.customerId">
                                                </el-option>
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="9">
                                        <el-form-item label="净重" class="form_item">
                                            <el-input id="netWeight" v-model="form.netWeight" clearable placeholder="请输入净重" :disabled="true"></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row :gutter="20">
                                    <el-col :span="15">
                                        <el-form-item label="货物名称" prop="materialId">
                                            <el-select id="materialId" @focus="getGoodsName" v-model="form.materialId" @change="materialChange" filterable clearable allow-create default-first-option placeholder="请输入货物名称">
                                                <el-option v-for="item in goodsNameOptions" :key="item.materialId" :label="item.materialName" :value="item.materialId">
                                                </el-option>
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="9">
                                        <el-form-item label="扣率" prop="cutPer" class="form_item" v-if="deductionMode == 'cutPer'">
                                            <el-input type="number" min="0" max="100" id="cutPer" v-model="form.cutPer" clearable placeholder="请输入扣率"></el-input>
                                        </el-form-item>
                                        <el-form-item label="扣重" class="form_item" v-else>
                                            <el-input type="number" min="0" id="cutWeight" v-model="form.cutWeight" clearable placeholder="请输入扣重"></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row :gutter="20">
                                    <el-col :span="15">
                                        <el-form-item label="规格名称">
                                            <el-select id="specificationName" @focus="getSpecificationName(form.materialId)" v-model="form.specId" filterable clearable allow-create default-first-option placeholder="请输入规格名称">
                                                <el-option v-for="item in specificationNameOptions" :key="item.specId" :label="item.specName" :value="item.specId">
                                                </el-option>
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="9">
                                        <el-form-item label="备注" class="form_item">
                                            <el-input id="remarks" v-model="form.remark" clearable placeholder="请输入备注"></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                            </el-form>
                        </div>
                    </div>
                    <div class="operation_container">
                        <div class="equipment_status">
                            <div>地感 <span :class="[frontCoil != 'OFF'?'green_color': 'red_color']"></span></div>
                            <div>光栅 <span :class="[frontRaster != 'OFF'?'green_color': 'red_color']"></span></div>
                            <div>地感 <span :class="[backCoil != 'OFF'?'green_color': 'red_color']"></span></div>
                            <div>光栅 <span :class="[backRaster != 'OFF'?'green_color': 'red_color']"></span></div>
                        </div>
                        <div class="operation_type">
                            <div class="overweight_type">
                                <div class="type_title">过磅类型:</div>
                                <el-radio-group id="weightType" v-model="form.weightType" @change="weightTypeChange(form.weightType,true)">
                                    <el-radio :label="item.value" size="medium" v-for="item in dict.type.business_type" :key="item.value">{{item.label}}</el-radio>
                                </el-radio-group>
                            </div>
                            <div class="overweight_type">
                                <div class="type_title">过磅模式:</div>
                                <el-radio-group :disabled="ModelStatus" id="weighingMode" v-model="form.weightMode" @change="weighingModeChange">
                                    <el-radio :label="item.value" size="medium" v-for="item in dict.type.weighing_mode" :key="item.value">{{item.label}}</el-radio>
                                </el-radio-group>
                            </div>
                        </div>
                        <div class="operation_auto">
                            <el-checkbox id="automaticPrinting" v-model="automaticPrinting">自动打印</el-checkbox>
                            <el-checkbox id="automaticWeighing" v-model="automaticWeighing">自动过磅</el-checkbox>
                        </div>
                        <div class="operation_btn">
                            <el-button icon="el-icon-goods" id="weighing" type="primary" :loading="weightLoading" @click="weighing(true)">称 重</el-button>
                            <el-button icon="el-icon-printer" id="print" type="primary" @click="printRecord">打 印</el-button>
                            <el-button icon="el-icon-files" id="saveSkin" type="primary" @click="saveSkinFun" :loading="saveSkinLoading">存 皮</el-button>
                            <el-button icon="el-icon-top" id="frontUp" type="primary" @click="LpcControl('A',true)">前 起</el-button>
                            <el-button icon="el-icon-top" id="backUp" type="primary" @click="LpcControl('B',true)">后 起</el-button>
                            <el-button icon="el-icon-refresh" id="clear" type="primary" @click="resetForm">清 空</el-button>
                            <el-button icon="el-icon-bottom" id="frontDown" type="primary" @click="LpcControl('A',false)">前 落</el-button>
                            <el-button icon="el-icon-bottom" id="backDown" type="primary" @click="LpcControl('B',false)">后 落</el-button>
                            <el-button icon="el-icon-switch-button" id="reset" type="primary" @click="hardwareReset">复 位</el-button>
                        </div>
                    </div>
                </div>
                <div class="video_container" v-if="videoStatus">
                    <!-- <video id="video" controls autoplay muted style="object-fit: fill"></video>
                    <video id="video2" controls autoplay muted style="object-fit: fill"></video>
                    <video id="video3" controls autoplay muted style="object-fit: fill"></video>
                    <video id="video4" controls autoplay muted style="object-fit: fill"></video> -->
                    <!-- <div id="divPlugin" style="width:100%;height:100%;"></div> -->
                    <hkView></hkView>
                </div>
            </div>
            <div :span="8" class="down_body">
                <el-table :data="tableData" style="width: 100%" border :row-style="selectedStyle" @row-dblclick="rowClick">
                    <el-table-column label="磅单ID" align="center" prop="weightId" v-if="false" />
                    <el-table-column label="预约单ID" align="center" prop="orderId" v-if="false" />
                    <el-table-column label="磅单编码" align="center" prop="weightCode" />
                    <el-table-column label="磅单类型" align="center" prop="weightType">
                        <template slot-scope="scope">
                            <dict-tag :options="dict.type.business_type" :value="scope.row.weightType" />
                        </template>
                    </el-table-column>
                    <el-table-column label="过磅模式" align="center" prop="weightMode">
                        <template slot-scope="scope">
                            <dict-tag :options="dict.type.weighing_mode" :value="scope.row.weightMode" />
                        </template>
                    </el-table-column>
                    <el-table-column label="车牌号码" align="center" prop="licensePlate" />
                    <el-table-column label="客户名称" align="center" prop="customerName" />
                    <el-table-column label="供应商名" align="center" prop="supplierName" />
                    <el-table-column label="物料类别" align="center" prop="categoryId" v-if="false">
                        <template slot-scope="scope">
                            <dict-tag :options="dict.type.driver_type" :value="scope.row.categoryId" />
                        </template>
                    </el-table-column>
                    <el-table-column label="物料名称" align="center" prop="materialName" />
                    <el-table-column label="规格名称" align="center" prop="specName" />
                    <el-table-column label="毛重" align="center" prop="weightM" />
                    <el-table-column label="皮重" align="center" prop="weightP" />
                    <el-table-column label="净重" align="center" prop="netWeight" />
                    <el-table-column label="实重" align="center" prop="actualWeight" />
                    <el-table-column label="扣率" align="center" prop="cutPer" v-if="deductionMode == 'cutPer'" />
                    <el-table-column label="扣重" align="center" prop="cutWeight" v-if="deductionMode == 'cutWeight'" />
                    <el-table-column label="司机名称" align="center" prop="driverId" v-if="false">
                        <template slot-scope="scope">
                            <dict-tag :options="dict.type.driver_type" :value="scope.row.driverId" />
                        </template>
                    </el-table-column>
                    <el-table-column label="车队名称" align="center" prop="fleetId" v-if="false">
                        <template slot-scope="scope">
                            <dict-tag :options="dict.type.driver_type" :value="scope.row.fleetId" />
                        </template>
                    </el-table-column>
                    <el-table-column label="地磅编号" align="center" prop="dbCodeFirst" />
                    <el-table-column label="二次过磅地磅编号" align="center" prop="dbCodeSecond" v-if="false" />
                    <el-table-column label="一次过磅时间" align="center" prop="weighFirstTime" width="120">
                        <template slot-scope="scope">
                            <span>{{ parseTime(scope.row.weightFirstTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="二次过磅时间" align="center" prop="weighSecondTime" width="120">
                        <template slot-scope="scope">
                            <span>{{ parseTime(scope.row.weightSecondTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="过磅员" align="center" prop="weightFirstBy" />
                    <el-table-column label="二次称重过磅员" align="center" prop="weightSecondBy" v-if="false" />
                    <el-table-column label="扣杂人" align="center" prop="cutBy" v-if="false" />
                    <el-table-column label="磅单状态" align="center" prop="billStatus">
                        <template slot-scope="scope">
                            <dict-tag :options="dict.type.pound_status" :value="scope.row.billStatus" />
                        </template>
                    </el-table-column>
                    <el-table-column label="上传状态" align="center" prop="uploadStatus" v-if="false">
                        <template slot-scope="scope">
                            <dict-tag :options="dict.type.upload_status" :value="scope.row.uploadStatus" />
                        </template>
                    </el-table-column>
                    <el-table-column label="作废状态" align="center" prop="nullifyStatus" v-if="false">
                        <template slot-scope="scope">
                            <dict-tag :options="dict.type.invalid_state" :value="scope.row.nullifyStatus" />
                        </template>
                    </el-table-column>
                    <el-table-column label="备注" align="center" prop="remark" />
                    <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                        <template slot-scope="scope">
                            <div style="display: flex; justify-content: flex-end; gap: 8px; flex-wrap: wrap;">
                                <el-button size="mini" type="primary" icon="el-icon-picture-outline" @click="imgViewVisibleFunction(scope.row.weightId)">图片查看</el-button>
                                <el-button size="mini" type="primary" icon="el-icon-video-camera" @click="videoViewVisibleFunction(scope.row.weightId)">视频查看</el-button>
                            </div>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </el-main>
        <el-footer class="footer">
            <div class="screening">
                <div class="screening_box">
                    <el-select id="value1" v-model="query.dataType" @change="handleDataTypeChange" placeholder="请选择" class="foot_select">
                        <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"></el-option>
                    </el-select>

                    <el-select @change="handleBillStatusChange" id="value2" v-model="query.billStatus" collapse-tags style="margin-left: 20px;" placeholder="请选择" class="foot_select">
                        <el-option key="item" label="全部记录" :value="undefined"></el-option>
                        <el-option v-for="item in dict.type.pound_status" :key="item.value" :label="item.label" :value="item.value"></el-option>
                    </el-select>
                    <!-- 分页组件 -->
                    <pagination
                        v-show="total > 0"
                        :total="total"
                        :page.sync="query.pageNum"
                        :limit.sync="query.pageSize"
                        @pagination="handlePagination"
                        layout="total, sizes, prev, pager, next"
                        style="background: #253e92; padding: 10px 0;"
                    />
                    <el-button id="search" type="primary" icon="el-icon-picture-outline" @click="imgViewVisibleFunction" v-if="false">图 片 查 看</el-button>
                    <el-button id="search" type="primary" icon="el-icon-video-camera" @click="videoViewVisibleFunction" v-if="false">视 频 查 看</el-button>
                </div>
                <div class="cumulative">
                    <div class="weight-summary">
                        <span class="summary-item">总车次: {{ total }}车</span>
                        <span class="summary-item">总皮重: {{ totalTareWeight }}kg</span>
                        <span class="summary-item">总毛重: {{ totalGrossWeight }}kg</span>
                        <span class="summary-item">总净重: {{ totalNetWeight }}kg</span>
                        <span class="summary-item">总实重: {{ totalActualWeight }}kg</span>
                    </div>
                </div>
            </div>
            <div class="info_box">华星智能称重管理系统 | 联系人:贺先生 | 联系电话:18674839841</div>
        </el-footer>
    </el-container>
    <img-view ref="imgView"></img-view>
    <video-view ref="videoView"></video-view>
    <!-- <InstrumentConfig ref="InstrumentConfig" :visible="true"></InstrumentConfig> -->
    <!-- <PoundPrint :printData="poundData" /> -->
</div>
</template>

<script>
import {
    listCustomer,
} from "@/api/basicInfo/customer";
import {
    updateConfig,
    listConfig
} from "@/api/system/config";
import {
    listVehicle,
} from "@/api/basicInfo/vehicle";
import {
    listSupplier,
} from "@/api/basicInfo/supplier";
import {
    listMaterial,
} from "@/api/basicInfo/material";
import {
    openSocket,
    sendMsg,
    endSocket
} from "@/utils/webSocket";
import {
    listWeightImg,
} from "@/api/weight/weightImg";
import {
    listWeightVideo,
} from "@/api/weight/weightVideo";
import {
    listSpec,
} from "@/api/basicInfo/spec";
import {
    listWeight,
    weight,
    saveSkin,
    licensePlateCallback,
} from "@/api/weight/weight";
import imgView from "@/components/imgView";
import videoView from '@/components/videoView/index.vue'
import InstrumentConfig from '@/components/InstrumentConfig/index.vue'
import PoundPrint from '@/components/printingComponent/index.vue'
import hkView from '@/components/hkView/index.vue'
import Pagination from '@/components/Pagination'
import {
    listWeighbridge,
} from "@/api/system/weighbridge";
import {
    log
} from "three";
import {
    Model
} from "echarts";

export default {
    components: {
        imgView, // 图片组件
        videoView, // 视频组件
        InstrumentConfig, // 仪器配置组件
        PoundPrint, // 磅单打印组件
        hkView, // 海康视频组件
        Pagination, // 分页组件
    },
    dicts: ['driver_type', 'business_type', 'pound_status', 'upload_status', 'invalid_state', 'weighing_mode', 'video_address'],
    data() {
        return {
            tableData: [],
            total: 0, // 总记录数
            allTableData: [], // 存储所有数据用于总重量计算
            poundData: {
                title: '磅单',
                carNo: '粤A12345',
                weight: '5000kg'
                // ... 其他字段
            },
            steadyState: false,
            checked: false,
            value2: undefined,
            value1: undefined,
            options: [{
                    value: 1,
                    label: '今日数据'
                },
                {
                    value: 2,
                    label: '昨日数据'
                },
                {
                    value: 3,
                    label: '本周数据'
                },
                {
                    value: 4,
                    label: '本月数据'
                },
                {
                    value: 5,
                    label: '本年数据'
                },
            ],
            form: {
                billStatus: undefined,
                categoryId: undefined,
                categoryName: undefined,
                customerId: undefined,
                customerName: undefined,
                dbCode: undefined,
                driverId: undefined,
                driverName: undefined,
                materialId: undefined,
                materialName: undefined,
                specId: undefined,
                specName: undefined,
                supplierId: undefined,
                supplierName: undefined,
                vehicleId: undefined,
                vehicleName: undefined,
                weight: 0,
                weightM: 0,
                weightP: 0,
                weightType: undefined,
                netWeight: 0,
                imgsPath: undefined,
                videosPath: undefined,
                licenseImgPath: undefined,
                weightMode: undefined,
                presetTare: 0,
                cutPer: undefined, //扣重
                cutWeight: undefined, //扣重
            },
            rules: {
                licensePlate: [{
                    required: true,
                    message: '请输入车牌号码',
                    trigger: 'change'
                }],
                supplierId: [{
                    required: true,
                    message: '请选择供应商',
                    trigger: 'change'
                }],
                customerId: [{
                    required: true,
                    message: '请选择客户',
                    trigger: 'change'
                }],
                materialId: [{
                    required: true,
                    message: '请选择货物名称',
                    trigger: 'change'
                }],
            },
            licensePlateOptions: [],
            supplierOptions: [],
            customerOptions: [],
            goodsNameOptions: [],
            webUrl: undefined, //视频查看路径
            webRtcServer1: null, // 视频流实例1
            webRtcServer2: null, // 视频流实例2 
            webRtcServer3: null, // 视频流实例3
            webRtcServer4: null, // 视频流实例4
            video_display1: undefined, //1路视频
            video_display2: undefined, //2路视频
            video_display3: undefined, //3路视频
            video_display4: undefined, //4路视频
            frontCoil: 'ON', //前地感
            backCoil: 'ON', //后地感
            frontRaster: 'ON', //前光栅
            backRaster: 'ON', //后光栅
            automaticPrinting: false, //自动打印
            automaticWeighing: false, //自动称重
            filters: [], //过滤条件
            loading: false,
            weightNum: 0, //仪表值
            timeWeight: null, //称重定时器
            autoLicense: true, //自动获取车牌
            rasterStatus: false, //光栅状态
            currentRowId: undefined, //当前选中行
            deductionMode: 'ratio', //扣重方式
            specificationNameOptions: [], //规格名称
            dbCode: undefined, //地磅编号
            basicInfoQuery: {
                pageNum: 1,
                pageSize: 9999999,
                status: 'Y',
            },
            //表单查询参数
            query: {
                pageNum: 1,
                pageSize: 20,
                billStatus: undefined,
                dataType: 1,
                startDate: undefined, // 开始时间
                endDate: undefined,   // 结束时间
            },
            weightLoading: false, //过磅动画
            saveSkinLoading: false, //保存皮重动画
            hardwareServiceUrl: undefined, //硬件服务地址
            viewUrl: undefined, //视频查看地址
            isCheckRadar: undefined, //是否检查观赏
            weightStatus: true, //称重节流
            isCheckWeighbridge: undefined, //是否检查地磅
            videoStatus: false, //视频状态
            ModelStatus: false, //模式状态
        };
    },
    watch: {
        automaticPrinting(value) {
            listConfig({
                configKey: 'automaticPrinting'
            }).then(response => {
                if (response.rows.length > 0) {
                    const data = {
                        ...response.rows[0],
                        configValue: value
                    }
                    this.updateConfig(data)
                }
            })
        },
        automaticWeighing(value) {
            listConfig({
                configKey: 'automaticWeighing'
            }).then(response => {
                if (response.rows.length > 0) {
                    const data = {
                        ...response.rows[0],
                        configValue: value
                    }
                    this.updateConfig(data)
                }
            })
        },
    },
    computed: {
        theme() {
            return {
                '--main-bg': this.$store.state.settings.theme, // 修改背景颜色
            }
            return this.$store.state.settings.theme;
        },
        // 计算总皮重
        totalTareWeight() {
            return this.allTableData.reduce((total, item) => {
                return total + (parseFloat(item.weightP) || 0);
            }, 0).toFixed(2);
        },
        // 计算总毛重
        totalGrossWeight() {
            return this.allTableData.reduce((total, item) => {
                return total + (parseFloat(item.weightM) || 0);
            }, 0).toFixed(2);
        },
        // 计算总净重
        totalNetWeight() {
            return this.allTableData.reduce((total, item) => {
                return total + (parseFloat(item.netWeight) || 0);
            }, 0).toFixed(2);
        },
        // 计算总实重
        totalActualWeight() {
            return this.allTableData.reduce((total, item) => {
                return total + (parseFloat(item.actualWeight) || 0);
            }, 0).toFixed(2);
        }
    },
    mounted() {
        this.getBasicInfo();
        // this.getCamera();
        this.$nextTick(() => {
            this.initConfig(); // 确保在页面渲染完成后初始化配置
        })
        this.videoStatus = true;
    },
    methods: {
        dealSocketMsgOne(data) {
            // if (this.appointWeight) {
            //     this.form.weighNum = this.dbOne.weighNum;
            //     this.form.stableWeighNum = sessionStorage.getItem("stableWeighNum");
            //     this.form.systemCode = this.dbOne.systemCode;
            //     this.form.imgsPath = this.dbOne.imgsPath;
            //     this.form.videosPath = this.dbOne.videosPath;
            //     this.form.inout = this.dbOne.inOut;
            // }

            //接收仪表值
            if (data.type >= 20000 ? (data.type < 30000 ? true : false) : false) {
                this.weightNum = data.data.weight; //仪表值
                this.steadyState = data.data.isStable; //稳定值
                //实时计算净重
                // console.log('weightType:', this.form.weightType, typeof this.form.weightType);
                const weightType = (this.form.weightType || '').toString().trim();
                if (weightType === 'receive_weight' && this.form.weightM > 0) {
                    this.form.weightP = data.data.weight;
                    if (this.form.cutPer) {
                        this.form.netWeight = ((Number(this.form.weightM) - Number(this.form.weightP)) - ((Number(this.form.weightM) - Number(this.form.weightP)) * Number(this.form.cutPer) / 100)).toFixed(0);
                    } else if (this.form.cutWeight) {
                        this.form.netWeight = (Number(this.form.weightM) - Number(this.form.weightP) - Number(this.form.cutWeight)).toFixed(0);
                    } else {
                        this.form.netWeight = (Number(this.form.weightM) - Number(this.form.weightP)).toFixed(0)
                    }
                } else if (weightType === 'ship_weight' && this.form.weightP > 0) {
                    this.form.weightM = data.data.weight;
                    if (this.form.cutPer) {
                        this.form.netWeight = ((Number(this.form.weightM) - Number(this.form.weightP)) - ((Number(this.form.weightM) - Number(this.form.weightP)) * Number(this.form.cutPer) / 100)).toFixed(0);
                    } else if (this.form.cutWeight) {
                        this.form.netWeight = (Number(this.form.weightM) - Number(this.form.weightP) - Number(this.form.cutWeight)).toFixed(0);
                    } else {
                        this.form.netWeight = (Number(this.form.weightM) - Number(this.form.weightP)).toFixed(0)
                    }
                }
                if (data.data.isStable) {
                    this.form.presetTare = data.data.weight;
                    this.form.weight = data.data.weight;
                }
            }

            //获取车牌
            if (data.type == 40002 || data.type == 40003) {
                console.log(data);
                console.log("获取车牌");
                if (this.autoLicense) { //自动获取车牌
                    this.form.licensePlate = data.data.license;
                    this.licensePlateCallbackFun(this.form.licensePlate);
                    this.form.licenseColor = data.data.licenseColor;
                    this.form.licenseImgPath = data.data.licenseImgPath;
                    this.$forceUpdate();
                }
            }

            // 光栅信号
            if (data.type >= 50000 ? (data.type < 60000 ? true : false) : false) {
                this.frontCoil = data.data.X0;
                this.backCoil = data.data.X1;
                this.frontRaster = data.data.X2;
                this.backRaster = data.data.X3;
                if (data.data.X2 == "OFF" || data.data.X3 == "OFF") {
                    this.rasterStatus = false;
                }
                if (data.data.X2 == "ON" && data.data.X3 == "ON") {
                    this.rasterStatus = true;
                }
            }

            //获取硬件记录
            if (data.type == 60001) {
                console.log(data);
                console.log('硬件记录');
                this.form.systemCode = data.data.systemCode;
                this.form.imgsPath = data.data.imagesPath;
                this.form.videosPath = data.data.videosPath;
                this.form.licenseImgPath = data.data.licensePlateImage;
                this.form.inOut = data.data.enter
                console.log(this.form, 'this.form');
            }

            //接收车辆上磅信号
            if (data.type == 60002) {
                console.log(data);
                console.log("车辆上磅");
                this.autoLicense = false;
            }
            //接收稳定称重完成
            if (data.type == 60003) {
                console.log(data);
                if (this.automaticWeighing) {
                    this.timeWeighing();
                }
                console.log("称重");
            }
            //接收车辆下磅信号
            if (data.type == 60004) {
                console.log(data);
                console.log("车辆下磅");
                this.autoLicense = true;
                clearInterval(this.timeWeight);
                this.timeWeight = null; // 清空定时器
                this.weightStatus = true;
            }
        },
        //自动称重
        timeWeighing() {
            const that = this;
            if (that.timeWeight) {
                clearInterval(that.timeWeight);
                that.timeWeight = null; // 清空定时器
            }
            that.timeWeight = setInterval(() => {
                that.$refs['form'].validate(async (valid) => {
                    if (valid && that.steadyState) {
                        if (that.rasterStatus) {
                            clearInterval(that.timeWeight);
                            that.timeWeight = null; // 清空定时器
                            that.weighing();
                        }
                    }
                });
            }, 500);
        },
        //称重方法
        async weighing(flag = false) {
            if (!this.form.licensePlate) {
                this.$message.error('请输入车牌号');
                return;
            }
            if (this.form.weightType == 'receive_weight') {
                if (!this.form.supplierId) {
                    this.$message.error('请输入供应商');
                    return;
                }
            } else {
                if (!this.form.customerId) {
                    this.$message.error('请输入客户');
                    return;
                }
            }
            if (!this.form.materialId) {
                this.$message.error('请输入物料');
                return;
            }
            if (!this.steadyState) {
                this.$message.error('请等待称重稳定');
                return;
            }

            if (!this.rasterStatus && this.isCheckRadar == 'Y') {
                this.$message.error('请停适当位置');
                return;
            }
            if (!this.weightStatus && this.isCheckWeighbridge == 'Y') {
                this.$message.error('请先下磅再称重');
                return;
            }
            this.weightLoading = true;
            if (flag) {
                const data = await this.takePicture();
                this.form.imagesPath = data.data.filePath
            }
            this.form.dbCode = this.dbCode;
            this.submitForm();
        },
        //表单提交
        async submitForm() {
            const IsNum = /^\d+$/;
            if (!IsNum.test(this.form.supplierId)) {
                this.form.supplierName = this.form.supplierId;
                this.form.supplierId = undefined;
            }
            if (!IsNum.test(this.form.customerId)) {
                this.form.customerName = this.form.customerId;
                this.form.customerId = undefined;
            }
            if (!IsNum.test(this.form.materialId)) {
                this.form.materialName = this.form.materialId;
                this.form.materialId = undefined;
            }
            if (!IsNum.test(this.form.specId)) {
                this.form.specName = this.form.specId;
                this.form.specId = undefined;
            }
            // this.$refs['form'].validate(async (valid) => {
            //     if (!valid) {
            //         this.weightLoading = false;
            //         console.log('表单验证失败');
            //         return false;
            //     }
            // 处理自定义输入的非ID字段
            // 定义需要处理的字段数组
            const fields = ['supplierId', 'customerId', 'materialId', 'specId'];
            // 遍历每个字段
            fields.forEach(field => {
                // 检查字段值是否存在且不是纯数字(表示是自定义输入而非选择项)
                if (this.form[field] && !/^\d+$/.test(this.form[field])) {
                    // 将自定义输入的值同时保存到对应的Name字段中
                    this.form[field.replace('Id', 'Name')] = this.form[field];
                    this.form[field] = undefined; // 清空ID字段，只保留Name字段
                }
            });
            try {
                const res = await weight(this.form);
                if (res.code === 200) {
                    this.$message.success('称重成功');
                    this.weightStatus = false; //称重状态
                    //表单重置
                    this.resetForm();
                    //数据更新
                    this.getBasicInfo();
                    //道闸控制
                    if (this.form.inOut == 'A') {
                        this.LpcControl('B', true);
                    } else {
                        this.LpcControl('A', true);
                    }
                    //语音播报
                    this.voiceBroadcast(res.data);
                    //大屏幕显示
                    this.showBigScreen(res.data);
                    //打印记录
                    if (this.automaticPrinting && res.data.billStatus == "weighted") {
                        this.printRecord(res.data.weightId);
                    }
                    //称重业务校验判断
                    if (res.data.billStatus == "weighted") {
                        // this.getWeighbridgeType(res.data);
                    }
                } else {
                    this.$message.error(res.msg || '称重失败');
                }
            } catch (error) {
                this.$message.error('称重请求失败');
                console.error('称重错误:', error);
            } finally {
                this.weightLoading = false;
            }
            // });
        },
        // 存皮方法
        async saveSkinFun() {
            if (!this.form.licensePlate) {
                this.$message.error('请输入车牌号');
                return;
            }
            if (!this.steadyState) {
                this.$message.error('请等待称重稳定');
                return;
            }
            this.saveSkinLoading = true;
            try {
                const res = await saveSkin(this.form);
                this.saveSkinLoading = false;
                if (res.code == 200) {
                    this.$message.success('存皮成功');
                    this.resetForm();
                } else {
                    this.$message.error(res.msg || '存皮失败');
                }
            } catch (error) {
                this.$message.error('存皮请求失败');
                console.error('存皮错误:', error);
                this.saveSkinLoading = false;
            }
        },
        //清空表单
        resetForm() {
            this.form.licensePlate = undefined;
            this.form.materialId = undefined;
            this.form.materialName = undefined;
            this.form.supplierId = undefined;
            this.form.supplierName = undefined;
            this.form.customerId = undefined;
            this.form.customerName = undefined;
            this.form.specId = undefined;
            this.form.specName = undefined;
            this.form.weight = 0;
            this.form.weightM = 0;
            this.form.weightP = 0;
            this.form.netWeight = 0;
            this.form.weightNum = 0;
            this.form.cutPer = undefined;
            this.form.cutWeight = undefined;
            this.form.imgsPath = undefined;
            this.form.videosPath = undefined;
            this.form.licenseColor = undefined;
            this.form.licenseImgPath = undefined;
            this.form.remark = undefined;
            this.form.presetTare = 0;
            this.ModelStatus = false;
        },
        //车牌回调接口
        async licensePlateCallbackFun(data) {
            // 支持直接传递事件对象
            const licensePlate = typeof data === 'string' ? data : this.form.licensePlate;
            if (!licensePlate) {
                this.resetForm();
                return;
            }
            try {
                const res = await licensePlateCallback({
                    licensePlate
                });
                if (res.data.length > 0) {
                    this.ModelStatus = true;
                    const info = res.data[0];
                    this.form.materialId = info.materialId;
                    this.form.supplierId = info.supplierId;
                    this.form.customerId = info.customerId;
                    this.form.specId = info.specId;
                    this.form.weightM = info.weightM;
                    this.form.weightP = info.weightP;
                    this.form.netWeight = info.netWeight;
                    this.form.cutPer = info.cutPer;
                    this.form.cutWeight = info.cutWeight;
                    this.form.remark = info.remark;
                    this.form.weightType = info.weightType;
                    this.form.weightMode = info.weightMode;
                    this.weightTypeChange(this.form.weightType, false);
                    // this.form = { ...info };
                    // this.form = Object.assign({}, this.form, info);
                    console.log(this.form, '车牌回调接口');

                    this.$forceUpdate();
                } else {
                    this.ModelStatus = false;
                }
            } catch (error) {
                console.error('车牌回调接口错误:', error);
            }
        },

        //模式切换
        weighingModeChange(value) {
            listConfig({
                configKey: 'weighingMode'
            }).then(res => {
                if (res.rows.length > 0) {
                    const data = {
                        ...res.rows[0],
                        configValue: value
                    };
                    this.updateConfig(data)
                }
            })
        },
        //业务切换
        weightTypeChange(value, flag = false) {
            listConfig({
                configKey: 'weightType'
            }).then(res => {
                if (res.rows.length > 0) {
                    const data = {
                        ...res.rows[0],
                        configValue: value
                    };
                    this.updateConfig(data)
                }
            })
            if (flag) {
                this.licensePlateCallbackFun(this.form.licensePlate); // 回调车牌号
            }
            if (value == 'receive_weight') {
                this.rules.customerId[0].required = false;
                this.rules.supplierId[0].required = true;
            } else {
                this.rules.customerId[0].required = true;
                this.rules.supplierId[0].required = false;
            }
        },
        //物料选择
        materialChange(value) {
            this.form.specId = undefined;
            this.getSpecificationName(value)
        },
        //初始化参数
        async initConfig() {
            const that = this;
            //获取称重类型
            const weightType = await this.fetchConfigByKey('weightType');
            this.form.weightType = weightType.msg;
            this.weightTypeChange(weightType.msg);
            //获取自动打印
            const automaticPrinting = await this.fetchConfigByKey('automaticPrinting');
            this.automaticPrinting = automaticPrinting.msg == 'true' ? true : false;
            //获取自动称重
            const automaticWeighing = await this.fetchConfigByKey('automaticWeighing');
            this.automaticWeighing = automaticWeighing.msg == 'true' ? true : false;
            //获取地磅编号
            const dbCode = await this.fetchConfigByKey('leftDbCode');
            this.dbCode = dbCode.msg;
            //扣杂计算方式
            const deductionMode = await this.fetchConfigByKey('deduction_algorithm');
            this.deductionMode = deductionMode.msg;
            //获取过磅模式
            const weighingMode = await this.fetchConfigByKey('weighingMode');
            this.form.weightMode = weighingMode.msg;
            //获取websocket地址
            const weighbridgeData = await listWeighbridge({
                weighbridgeCode: this.dbCode
            });
            this.socketUrl = weighbridgeData.rows[0].wsUrl;
            openSocket(
                this.socketUrl,
                // 'ws://*************:18080/hardware-service-listener',
                function (msg) {
                    let data = JSON.parse(msg.data);
                    that.dealSocketMsgOne(data);
                }, 3000
            );
            //硬件服务地址
            const hardwareServiceUrl = await this.fetchConfigByKey('hardware_address');
            this.hardwareServiceUrl = hardwareServiceUrl.msg;
            //视图查看路径
            const viewUrl = await this.fetchConfigByKey('view_preview');
            this.viewUrl = viewUrl.msg;
            //是否检测光栅
            const isCheckRadar = await this.fetchConfigByKey('grating_detection');
            this.isCheckRadar = isCheckRadar.msg;
            //是否检测下磅
            const isCheckWeighbridge = await this.fetchConfigByKey('lost_weigh');
            this.isCheckWeighbridge = isCheckWeighbridge.msg;
        },
        //修改参数
        async updateConfig(data) {
            await updateConfig(data);
        },
        //参数查询方法封装
        fetchConfigByKey(key) {
            return new Promise((resolve, reject) => {
                this.getConfigKey(key)
                    .then(response => {
                        resolve(response);
                    })
                    .catch(error => {
                        reject(error);
                    });
            });
        },
        //基础信息查询
        getBasicInfo() {
            this.getCustomer();
            this.getSupplier();
            this.getLicensePlate();
            this.getGoodsName();
            this.getWeighbridgeList();
            this.getSpecificationName();
        },
        //客户数据查询
        async getCustomer() {
            try {
                const response = await listCustomer(this.basicInfoQuery);
                this.customerOptions = response.rows;
            } catch (error) {
                this.$message.error(error.msg);
            }
        },
        //供应商数据查询
        async getSupplier() {
            try {
                const response = await listSupplier(this.basicInfoQuery);
                this.supplierOptions = response.rows;
            } catch (error) {
                this.$message.error(error.msg);
            }
        },
        //货物名称数据查询
        async getGoodsName() {
            try {
                const response = await listMaterial(this.basicInfoQuery);
                this.goodsNameOptions = response.rows;
            } catch (error) {
                this.$message.error(error.msg);
            }
        },
        //规格名称数据查询
        async getSpecificationName(materialId) {
            let id = undefined;
            const IsNum = /^\d+$/;
            console.log(IsNum.test(materialId));

            if (IsNum.test(materialId)) {
                id = materialId;
            }
            try {
                const response = await listSpec({
                    materialId: id,
                    ...this.basicInfoQuery
                });
                this.specificationNameOptions = response.rows;
            } catch (error) {
                this.$message.error(error.msg);
            }
        },
        //车牌号码数据查询
        async getLicensePlate() {
            try {
                const response = await listVehicle(this.basicInfoQuery);
                this.licensePlateOptions = response.rows;
            } catch (error) {
                this.$message.error(error.msg);
            }
        },
        //称重列表查询
        async getWeighbridgeList() {
            try {
                // 根据dataType设置查询时间范围
                this.setQueryTimeRange();

                const response = await listWeight(this.query);

                this.tableData = response.rows || [];
                this.allTableData = response.rows || []; // 使用当前页数据计算总重量
                this.total = response.total || 0;
            } catch (error) {
                this.$message.error(error.msg);
            }
        },

        // 设置查询时间范围
        setQueryTimeRange() {
            const now = new Date();

            switch (this.query.dataType) {
                case 1: // 今天
                    const today = new Date(now);
                    today.setHours(0, 0, 0, 0);
                    const tomorrow = new Date(today);
                    tomorrow.setDate(today.getDate() + 1);
                    this.query.startDate = this.formatDateTime(today);
                    this.query.endDate = this.formatDateTime(tomorrow);
                    break;
                case 2: // 昨天
                    const yesterday = new Date(now);
                    yesterday.setDate(now.getDate() - 1);
                    yesterday.setHours(0, 0, 0, 0);
                    const todayStart = new Date(yesterday);
                    todayStart.setDate(yesterday.getDate() + 1);
                    this.query.startDate = this.formatDateTime(yesterday);
                    this.query.endDate = this.formatDateTime(todayStart);
                    break;
                case 3: // 本周
                    const startOfWeek = new Date(now);
                    startOfWeek.setDate(now.getDate() - now.getDay());
                    startOfWeek.setHours(0, 0, 0, 0);
                    const endOfWeek = new Date(startOfWeek);
                    endOfWeek.setDate(startOfWeek.getDate() + 6);
                    endOfWeek.setHours(23, 59, 59, 999);
                    this.query.startDate = this.formatDateTime(startOfWeek);
                    this.query.endDate = this.formatDateTime(endOfWeek);
                    break;
                case 4: // 本月
                    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
                    const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);
                    endOfMonth.setHours(23, 59, 59, 999);
                    this.query.startDate = this.formatDateTime(startOfMonth);
                    this.query.endDate = this.formatDateTime(endOfMonth);
                    break;
                case 5: // 本年
                    const startOfYear = new Date(now.getFullYear(), 0, 1);
                    const endOfYear = new Date(now.getFullYear(), 11, 31);
                    endOfYear.setHours(23, 59, 59, 999);
                    this.query.startDate = this.formatDateTime(startOfYear);
                    this.query.endDate = this.formatDateTime(endOfYear);
                    break;
                default:
                    // 清空时间范围
                    this.query.startDate = undefined;
                    this.query.endDate = undefined;
                    break;
            }
        },

        // 格式化日期时间
        formatDateTime(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            const seconds = String(date.getSeconds()).padStart(2, '0');
            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        },

        // 分页处理方法
        handlePagination(pagination) {
            this.query.pageNum = pagination.page;
            this.query.pageSize = pagination.limit;
            this.getWeighbridgeList();
        },

        // 数据类型改变处理
        handleDataTypeChange() {
            this.query.pageNum = 1; // 重置页码
            this.getWeighbridgeList();
        },

        // 磅单状态改变处理
        handleBillStatusChange() {
            this.query.pageNum = 1; // 重置页码
            this.getWeighbridgeList();
        },
        //称重页面类型校验
        getWeighbridgeType(result) {
            //收货过磅
            if (result.weightType == "receive_weight") {
                if (Number(result.weightFirst) < Number(result.weightSecond)) {
                    this.$notify.error({
                        title: '错误',
                        message: '请检查业务类型是否有误！磅单编码：' + result.weightCode,
                        duration: 0
                    });
                }
            } else if (result.weightType == "ship_weight") {
                //发货过磅
                if (Number(result.weightFirst) > Number(result.weightSecond)) {
                    this.$notify.error({
                        title: '错误',
                        message: '请检查业务类型是否有误！磅单编码：' + result.weightCode,
                        duration: 0
                    });
                }
            }
        },
        //记录打印
        async printRecord(id) {
            if (!this.hardwareServiceUrl) {
                this.$message.warning('请先配置硬件服务地址');
            };
            const url = this.hardwareServiceUrl + '/api/hardware/v3/activeX/printFile';
            const Id = this.currentRowId || id;
            if (!Id) {
                this.$message.warning('请先选择一条数据');
                return;
            }
            this.$axios.post(url, {
                    data: Id
                }).then((res) => {
                    console.log(res);
                    const result = JSON.parse(res.data.result)
                    if (result.code == 0) {
                        this.$message.success('打印成功');
                    } else {
                        this.$message.error('打印失败');
                    }
                })
                .catch((res) => {
                    // this.msgError("操作失败!")
                });
        },
        //道闸控制
        LpcControl(name, value, ) {
            if (!this.hardwareServiceUrl) {
                this.$message.warning('请先配置硬件服务地址');
            };
            const url = this.hardwareServiceUrl + '/api/hardware/v1/deviceOp/switchGate';
            console.log("道闸操作 ==》 道闸：" + name + "，动作：" + value);
            this.$axios.post(url, {
                    enter: name,
                    signal: value,
                })
                .then((res) => {
                    if (res.data.code == 500) {
                        console.log(res.data.msg);
                    }
                })
                .catch((res) => {
                    // this.msgError("操作失败!")
                });
        },
        /**
         * 大屏幕显示
         * 发送车牌号和重量信息到大屏幕显示
         */
        async showBigScreen(result) {
            if (!this.hardwareServiceUrl) {
                this.$message.warning('请先配置硬件服务地址');
            };
            const url = this.hardwareServiceUrl + '/api/hardware/v3/screen/showJlgScreen';
            //年
            let year = new Date().getFullYear();
            //月份是从0月开始获取的，所以要+1
            let month = new Date().getMonth() + 1;
            //日
            let day = new Date().getDate();
            let str;
            if (result.billStatus == "weighted") {
                str = `${result.licensePlate}，一次过磅${result.weightFirst}Kg，二次过磅${result.weightSecond}Kg，净重:${result.netWeight}Kg，${year}-${month}-${day}`;
            } else {
                str = `${result.licensePlate}，重量:${this.weightNum}Kg，${year}-${month}-${day}`;
            }
            const BigScreenObj = {
                deviceName: "大屏幕",
                text: str,
            }
            try {
                const {
                    data
                } = await this.$axios.post(url, BigScreenObj);
                console.log('大屏幕显示结果:', data);
                if (data.code === 500) {
                    console.error('大屏幕显示错误:', data.msg);
                    this.$message.error('大屏幕显示失败');
                }
            } catch (error) {
                console.error('大屏幕显示异常:', error);
                this.$message.error('大屏幕显示服务异常');
            }
        },
        //统一抓拍
        async takePicture() {
            if (!this.hardwareServiceUrl) {
                this.$message.warning('请先配置硬件服务地址');
            };
            const url = this.hardwareServiceUrl + '/api/hardware/v3/carmera/takePhotoAll/';
            try {
                const {
                    data
                } = await this.$axios.post(url);
                const result = JSON.parse(data.result);
                console.log('抓拍结果:', result);
                if (result.code === 500) {
                    console.error('抓拍错误:', result.message);
                    this.$message.error('抓拍失败');
                    return Promise.reject(result.message);
                }
                if (!result.data) {
                    return Promise.resolve({
                        data: {
                            data: {
                                filePath: ''
                            }
                        }
                    });
                } else {
                    return Promise.resolve(result);
                }
            } catch (error) {
                console.error('抓拍异常:', error);
                this.$message.error('抓拍服务异常');
                return Promise.resolve({
                    data: {
                        data: {
                            filePath: ''
                        }
                    }
                });
            }
        },

        /**
         * 语音播报 
         * 发送车牌号和重量信息进行语音播报
         */
        async voiceBroadcast(result) {
            if (!this.hardwareServiceUrl) {
                this.$message.warning('请先配置硬件服务地址');
            };
            const url = this.hardwareServiceUrl + '/api/hardware/v3/activeX/voicePlay';
            let str = `称重完成，车辆请下磅`;
            if (result.weightType == "weighted") {
                str = `称重完成，车牌${result.licensePlate}，一次过磅${result.weightFirst}千克，二次过磅${result.weightSecond}千克，净重${result.netWeight}千克，车辆请下磅`;
            } else {
                str = `称重完成，车牌${result.licensePlate}，重量${this.weightNum}千克，车辆请下磅`;
            }
            let voiceObj = {
                "text": str,
                "speed": 45,
                "volume": 100,
            }
            try {
                const {
                    data
                } = await this.$axios.post(url, voiceObj);
                console.log('语音播结果:', data);
                if (data.code === 500) {
                    console.error('语音播报错误:', data.msg);
                    this.$message.error('语音播报失败');
                }
            } catch (error) {
                console.error('语音播报异常:', error);
                this.$message.error('语音播报服务异常');
            }
        },
        //图片查看
        imgViewVisibleFunction(id) {
            const RowId = this.currentRowId || id;
            if (!RowId) {
                this.$message.warning('请先选择一条数据');
                return;
            }
            const urlList = []
            listWeightImg({
                weightId: RowId
            }).then(response => {
                if (response.rows.length == 0) {
                    this.$message.warning('暂无图片');
                    return;
                }
                response.rows.forEach(item => {
                    urlList.push(item.weightImgPath)
                })
                console.log(this.viewUrl);
                this.$hidPlugin()
                this.$refs.imgView.openDialog(urlList, this.viewUrl);
            });
        },
        //视频查看
        videoViewVisibleFunction(id) {
            const RowId = this.currentRowId || id;
            if (!RowId) {
                this.$message.warning('请先选择一条数据');
                return;
            }
            listWeightVideo({
                weightId: RowId
            }).then(response => {
                if (response.rows.length == 0) {
                    this.$message.warning('暂无视频');
                    return;
                }
                const urlList = response.rows.map(item => item.weightVideoPath);
                this.$hidPlugin()
                this.$refs.videoView.openDialog(urlList, this.viewUrl);
            });
        },
        //硬件复位
        async hardwareReset() {
            const url = this.hardwareServiceUrl + '/api/hardware/v1/deviceOp/reset';

            try {
                const {
                    data
                } = await this.$axios.post(url, {});
                console.log('硬件复位结果:', data);
                const result = JSON.parse(data.result)
                if (result.code == 0) {
                    this.autoLicense = true;
                    clearInterval(this.timeWeight);
                    this.timeWeight = null; // 清空定时器
                    this.weightStatus = true;
                    this.$message.success('硬件复位成功');
                }
            } catch (error) {
                console.error('硬件复位异常:', error);
                this.$message.error('硬件复位服务异常');
            }
        },
        //摄像头处理
        getCamera() {
            this.getConfigKey("web_url").then((response) => {
                this.webUrl = response.msg || '127.0.0.1:8000';
                console.log(this.dict.type.video_address, 'video_address');

                // 创建WebRTC流实例数组
                const videoStreams = [{
                        id: "video",
                        rtsp: 'rtsp://admin:hxcz12345678@************:554/cam/realmonitor?channel=10'
                    },
                    {
                        id: "video2",
                        rtsp: 'rtsp://admin:hxcz12345678@************:554/cam/realmonitor?channel=10'
                    },
                    {
                        id: "video3",
                        rtsp: 'rtsp://admin:hxcz12345678@************:554/cam/realmonitor?channel=10'
                    },
                    {
                        id: "video4",
                        rtsp: 'rtsp://admin:hxcz12345678@************:554/cam/realmonitor?channel=10'
                    }
                ];
                this.dict.type.video_address.forEach((item, index) => {
                    videoStreams[index].rtsp = item.value
                })
                console.log(videoStreams, 'video_address');
                // 批量创建WebRTC流实例
                // 遍历视频流配置数组，为每个视频流创建WebRTC实例并连接RTSP流
                videoStreams.forEach((stream, index) => {
                    // 动态创建webRtcServer1-4实例
                    this[`webRtcServer${index+1}`] = new WebRtcStreamer(
                        stream.id, // 视频元素ID
                        `${location.protocol}//${this.webUrl}` // WebRTC服务器地址
                    );
                    // 连接对应的RTSP视频流
                    this[`webRtcServer${index+1}`].connect(stream.rtsp);
                });

                // 视频事件处理
                const handleFullscreen = (video) => {
                    if (!document.fullscreenElement) {
                        const requestMethod =
                            video.requestFullscreen ||
                            video.mozRequestFullScreen ||
                            video.webkitRequestFullscreen;
                        requestMethod.call(video);
                    } else {
                        const exitMethod =
                            document.exitFullscreen ||
                            document.mozCancelFullScreen ||
                            document.webkitExitFullscreen;
                        exitMethod.call(document);
                    }
                };

                // 为所有视频元素添加事件监听
                document.querySelectorAll("video").forEach(video => {
                    video.addEventListener("click", e => e.preventDefault());
                    video.addEventListener("dblclick", () => handleFullscreen(video));
                });
            });
        },
        //选择某一行改变颜色确定选中行
        selectedStyle({
            row,
            rowIndex
        }) {
            if (this.currentRowId == row.weightId) {
                return {
                    "background-color": '#1890ff',
                    "color": '#fff'
                };
            }
        },
        //表格行点击事件
        rowClick(row, column, event) {
            this.currentRowId = row.weightId;
            this.form = {
                ...this.form,
                ...row, // 将当前行的数据合并到form对象中
            }
            this.form.netWeight = 0
            this.form.weightM = 0
            this.form.weightP = 0
            // this.weightTypeChange(this.form.weightType);
            this.licensePlateCallbackFun(this.form.licensePlate); // 回调车牌号
        },
        //表格时间排序
        filterHandler(value, row, column) {
            const property = column['property'];
            return row[property] === value;
        },
    },
    beforeDestroy() {
        this.$hidPlugin()
        this.videoStatus = false; // 停止视频流
        endSocket();
    },
};
</script>

<style lang="scss" scoped>
@font-face {
    font-family: electronicFont;
    src: url(../../../assets/font/DS-DIGIT.TTF);
}

.weight {
    .el-main {
        height: calc(100vh - 110px);
        background-color: #0e5495 !important;
        padding: 0;
        padding-top: 10px;

        .red_color {
            background-color: red !important;
            width: 25px;
            height: 25px;
            border-radius: 50%;
            display: inline-block;
        }

        .green_color {
            background-color: rgb(17, 194, 17) !important;
            width: 25px;
            height: 25px;
            border-radius: 50%;
            display: inline-block;
        }

        .top_body {
            display: flex;
            height: 60%;

            .weight_container {
                flex: 1;
                display: flex;

                .form_container {
                    flex: 1;
                    display: flex;
                    flex-wrap: wrap;
                    align-items: center;
                    justify-content: center;

                    .weight_info {
                        width: 100%;

                        .weight_num {
                            width: 80%;
                            margin: 0 auto;
                            font-size: 100px;
                            background-color: #1890ff;
                            color: #fff;
                            height: 115px;
                            border-radius: 10px;
                            display: flex;
                            /* text-align: center; */
                            justify-content: center;
                            align-items: center;
                            font-family: electronicFont;
                        }

                        .steady_state {
                            width: 100%;
                            height: 35px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            color: #fff;

                            div {
                                margin: 0 5px;
                                font-size: 18px;
                            }

                            .steady_state_round {
                                width: 25px;
                                height: 25px;
                                border-radius: 50%;
                                background-color: #7f7f7f;
                                display: inline-block;

                            }
                        }

                    }

                    .weight_form {
                        margin-top: 10px;
                        width: 100%;

                        ::v-deep .el-input__inner {
                            height: 45px !important;
                            font-size: 18px;
                            border-color: #e5e7eb;
                        }

                        ::v-deep.el-select {
                            width: 100%;
                        }

                        ::v-deep .el-form-item__label {
                            font-size: 18px;
                            line-height: 43px;
                            color: #fff;
                        }

                        ::v-deep .el-form-item {
                            margin-bottom: 20px;
                        }

                        ::v-deep .el-form-item__error {
                            display: none;
                        }

                        .form_item {
                            box-sizing: border-box;
                            // padding-right: 45px;

                            ::v-deep .el-form-item__label {
                                width: 56px !important;
                            }

                            ::v-deep .el-form-item__content {
                                margin-left: 56px !important;
                            }
                        }
                    }
                }

                .operation_container {
                    width: 30%;
                    height: 100%;
                    display: flex;
                    flex-wrap: wrap;
                    align-items: center;
                    justify-content: center;

                    // background-color: blue;
                    .equipment_status {
                        width: 90%;
                        height: 100px;
                        // margin: 0 auto;
                        border: #b7b7be solid 1px;
                        display: flex;
                        flex-wrap: wrap;
                        justify-content: space-evenly;
                        border-radius: 5px;

                        div {
                            width: 50%;
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            font-size: 18px;
                            color: #fff;

                            span {
                                margin-left: 15px;
                            }
                        }
                    }

                    .operation_type {
                        // margin-top: 10px;
                        display: flex;
                        width: 90%;
                        justify-content: space-between;

                        .overweight_type {
                            width: 48%;
                            border: #b7b7be solid 1px;
                            border-radius: 5px;
                            box-sizing: border-box;
                            padding: 5px;

                            .type_title {
                                font-size: 1.041667vw;
                                font-weight: 600;
                                color: #fff;
                            }

                            ::v-deep.el-radio {
                                height: 30px;
                                display: flex;
                                align-items: center;
                            }

                            ::v-deep.el-radio-group {
                                display: flex;
                                flex-direction: column;
                                margin-left: 0.9375vw;
                            }

                            ::v-deep.el-radio__label {
                                font-size: 0.9375vw;
                                font-weight: bold;
                                color: #fff;
                            }
                        }
                    }

                    .operation_auto {
                        width: 90%;
                        // margin-top: 10px;
                        border: #b7b7be solid 1px;
                        border-radius: 5px;
                        box-sizing: border-box;
                        padding: 10px;
                        display: flex;
                        justify-content: space-evenly;
                        align-items: center;

                        ::v-deep.el-checkbox__label {
                            font-size: 0.9375vw;
                            font-weight: bold;
                            color: #fff;
                        }

                        ::v-deep.el-checkbox__inner {
                            height: 0.9375vw;
                            width: 0.9375vw;
                        }

                        ::v-deep.el-checkbox__inner::after {
                            left: 0.3125vw;
                            top: 0.15625vw;
                        }
                    }

                    .operation_btn {
                        width: 90%;
                        // margin-top: 10px;
                        display: flex;
                        flex-wrap: wrap;
                        justify-content: space-between;
                        align-items: center;

                        ::v-deep.el-button {
                            margin: 0;
                            margin-top: 13px;
                            width: calc(33.3% - 10px);
                            font-size: 0.9375vw;
                            padding: 0.520833vw 0.520833vw;
                        }
                    }
                }
            }

            .video_container {
                height: 100%;
                width: 40%;
                // background-color: red;
                display: flex;
                flex-wrap: wrap;
                justify-content: space-evenly;
                align-content: stretch;

                video {
                    width: 48%;
                    height: 49%;
                }

            }
        }

        .down_body {
            // background-color: #fff;
            background: #253e92;
            height: 40%;
            overflow: auto;

            ::v-deep.cell {
                font-size: 18px;
            }

            ::v-deep.el-table__header-wrapper th {
                // background-color: #b5b5b5;
                background: #253e92;
                color: #fff;
                font-size: 18px;
                font-weight: 600;
            }

            ::v-deep.el-table__body tr:hover>td {
                background-color: #1890ff !important;
                color: #fff;
            }

            ::v-deep.el-table__empty-block {
                background: #253e92;
            }

            ::v-deep.el-table__row {
                background-color: #253e92;
            }

            ::v-deep.el-table__cell {
                color: #fff;
            }

        }
    }

    .footer {
        // background-color: #eaeaea;
        background: #253e92;
        padding: 0;
        font-size: 18px;

        ::v-deep.el-select {
            margin-left: 10px;
        }

        .screening {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 20px;
            height: 30px;

            .screening_box {
                display: flex;
                align-items: center;

                ::v-deep .el-input__inner {
                    background: #e2e6eb !important;
                    border-color: #000 !important;
                    height: 30px;
                    width: 150px;
                    line-height: 30px;
                    color: black;
                }

                ::v-deep.el-input__inner::placeholder {
                    color: black;
                }

                ::v-deep.el-input__icon {
                    line-height: 30px;
                    color: black;
                }

                ::v-deep .el-button {
                    width: 150px;
                    height: 25px !important;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    margin-left: 20px;
                }
            }

            .cumulative {
                .weight-summary {
                    display: flex;
                    gap: 20px;

                    .summary-item {
                        color: #fff;
                        font-size: 14px;
                        font-weight: bold;
                        background: rgba(255, 255, 255, 0.1);
                        padding: 2px 10px;
                        border-radius: 4px;
                        border: 1px solid rgba(255, 255, 255, 0.3);
                    }
                }
            }
        }

        .info_box {
            background-color: #1890ff;
            height: 30px;
            color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 16px;
        }

        // 分页组件样式
        ::v-deep .pagination-container {
            margin-top: 0px !important;
            background: transparent !important;

            .el-pagination {

                .el-pagination__total,
                .el-pagination__jump,
                .el-pager li,
                .el-pagination__sizes .el-input__inner,
                .btn-prev,
                .btn-next {
                    color: #fff !important;
                    background: transparent !important;
                    border-color: rgba(255, 255, 255, 0.3) !important;
                }

                .el-pager li:hover,
                .el-pager li.active {
                    background: #1890ff !important;
                    color: #fff !important;
                }

                .btn-prev:hover,
                .btn-next:hover {
                    background: rgba(255, 255, 255, 0.1) !important;
                }

                .el-pagination__sizes .el-input .el-input__inner {
                    background: rgba(255, 255, 255, 0.1) !important;
                    border-color: rgba(255, 255, 255, 0.3) !important;
                    color: #fff !important;
                }
            }
        }
    }
}
</style>
